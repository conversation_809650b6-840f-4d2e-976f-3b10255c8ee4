<?php
/**
 * Admin interface controller for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Admin Class
 */
class PBC_Admin {

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    private $pricing_engine;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Constructor
     *
     * @param PBC_Database $database Database instance
     * @param PBC_Pricing_Engine $pricing_engine Pricing engine instance
     */
    public function __construct($database, $pricing_engine) {
        $this->database = $database;
        $this->pricing_engine = $pricing_engine;
        $this->error_handler = PBC_Error_Handler::get_instance();
        
        $this->init_hooks();
    }

    /**
     * Initialize admin hooks
     */
    private function init_hooks() {
        // Product-level pricing interface
        add_action('woocommerce_product_options_pricing', array($this, 'add_product_pricing_fields'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_pricing_rules'));
        
        // Category-level pricing interface
        $this->init_category_hooks();
        
        // Global pricing settings interface
        $this->init_global_hooks();
        
        // Import/Export functionality
        $this->init_import_export_hooks();
        
        // Admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // AJAX handlers
        add_action('wp_ajax_pbc_get_countries', array($this, 'ajax_get_countries'));
        add_action('wp_ajax_pbc_save_product_rule', array($this, 'ajax_save_product_rule'));
        add_action('wp_ajax_pbc_delete_product_rule', array($this, 'ajax_delete_product_rule'));
        
        // Dashboard AJAX handlers
        add_action('wp_ajax_pbc_toggle_rule_status', array($this, 'ajax_toggle_rule_status'));
        add_action('wp_ajax_pbc_delete_rule', array($this, 'ajax_delete_rule'));
        add_action('wp_ajax_pbc_bulk_action_rules', array($this, 'ajax_bulk_action_rules'));
        add_action('wp_ajax_pbc_edit_rule', array($this, 'ajax_edit_rule'));
        add_action('wp_ajax_pbc_get_rule_data', array($this, 'ajax_get_rule_data'));
        add_action('wp_ajax_pbc_cleanup_cache', array($this, 'ajax_cleanup_cache'));
        add_action('wp_ajax_pbc_detect_conflicts', array($this, 'ajax_detect_conflicts'));
        
        // Country Detection AJAX handlers
        add_action('wp_ajax_pbc_save_detection_settings', array($this, 'ajax_save_detection_settings'));
        add_action('wp_ajax_pbc_test_ip_detection', array($this, 'ajax_test_ip_detection'));
        add_action('wp_ajax_pbc_test_user_detection', array($this, 'ajax_test_user_detection'));
        add_action('wp_ajax_pbc_view_detection_logs', array($this, 'ajax_view_detection_logs'));
        add_action('wp_ajax_pbc_clear_detection_logs', array($this, 'ajax_clear_detection_logs'));
        add_action('wp_ajax_pbc_clear_logs', array($this, 'ajax_clear_logs'));
    }

    // ========================================
    // PRODUCT-LEVEL PRICING INTERFACE
    // ========================================

    /**
     * Add country pricing fields to product edit page
     */
    public function add_product_pricing_fields() {
        global $post;
        
        if (!$post || $post->post_type !== 'product') {
            return;
        }

        $product_id = $post->ID;
        $existing_rules = $this->database->get_pricing_rules_by_type('product', $product_id);
        
        ?>
        <div class="options_group pbc-product-pricing">
            <p class="form-field">
                <label><?php _e('Country-Based Pricing', 'price-by-country'); ?></label>
                <span class="description"><?php _e('Set different prices for specific countries. Leave empty to use category or global pricing.', 'price-by-country'); ?></span>
            </p>
            
            <div id="pbc-product-rules-container">
                <?php if (!empty($existing_rules)): ?>
                    <?php foreach ($existing_rules as $rule): ?>
                        <?php $this->render_product_rule_row($rule); ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <p class="form-field">
                <button type="button" id="pbc-add-product-rule" class="button">
                    <?php _e('Add Country Pricing Rule', 'price-by-country'); ?>
                </button>
            </p>
        </div>

        <!-- Hidden template for new rules -->
        <script type="text/template" id="pbc-product-rule-template">
            <?php $this->render_product_rule_row(); ?>
        </script>
        <?php
    }

    /**
     * Render a single product pricing rule row
     *
     * @param object|null $rule Existing rule data
     */
    private function render_product_rule_row($rule = null) {
        $rule_id = $rule ? $rule->id : '';
        $country_code = $rule ? $rule->country_code : '';
        $adjustment_type = $rule ? $rule->adjustment_type : 'percentage';
        $adjustment_value = $rule ? $rule->adjustment_value : '';
        $is_active = $rule ? $rule->is_active : 1;
        
        ?>
        <div class="pbc-rule-row" data-rule-id="<?php echo esc_attr($rule_id); ?>">
            <div class="pbc-rule-fields">
                <select name="pbc_country_code[]" class="pbc-country-select" required>
                    <option value=""><?php _e('Select Country', 'price-by-country'); ?></option>
                    <?php echo $this->get_countries_options($country_code); ?>
                </select>
                
                <select name="pbc_adjustment_type[]" class="pbc-adjustment-type">
                    <option value="percentage" <?php selected($adjustment_type, 'percentage'); ?>>
                        <?php _e('Percentage (%)', 'price-by-country'); ?>
                    </option>
                    <option value="fixed" <?php selected($adjustment_type, 'fixed'); ?>>
                        <?php _e('Fixed Amount', 'price-by-country'); ?>
                    </option>
                </select>
                
                <input type="number" 
                       name="pbc_adjustment_value[]" 
                       class="pbc-adjustment-value" 
                       value="<?php echo esc_attr($adjustment_value); ?>" 
                       step="0.01" 
                       placeholder="0.00" 
                       required />
                
                <label class="pbc-active-label">
                    <input type="checkbox" 
                           name="pbc_is_active[]" 
                           value="1" 
                           <?php checked($is_active, 1); ?> />
                    <?php _e('Active', 'price-by-country'); ?>
                </label>
                
                <button type="button" class="button pbc-remove-rule">
                    <?php _e('Remove', 'price-by-country'); ?>
                </button>
            </div>
            
            <input type="hidden" name="pbc_rule_id[]" value="<?php echo esc_attr($rule_id); ?>" />
        </div>
        <?php
    }

    /**
     * Save product pricing rules
     *
     * @param int $product_id Product ID
     */
    public function save_product_pricing_rules($product_id) {
        try {
            // Verify nonce for security
            if (!isset($_POST['woocommerce_meta_nonce']) || 
                !wp_verify_nonce($_POST['woocommerce_meta_nonce'], 'woocommerce_save_data')) {
                throw new Exception('Security verification failed');
            }

            // Check user permissions
            if (!current_user_can('edit_product', $product_id)) {
                throw new Exception('Insufficient permissions to edit product pricing');
            }
            
        } catch (Exception $e) {
            $this->error_handler->handle_admin_error($e, [
                'product_id' => $product_id,
                'function' => __FUNCTION__,
                'user_id' => get_current_user_id()
            ]);
            return;
        }

        // Get submitted rule data
        $country_codes = isset($_POST['pbc_country_code']) ? $_POST['pbc_country_code'] : array();
        $adjustment_types = isset($_POST['pbc_adjustment_type']) ? $_POST['pbc_adjustment_type'] : array();
        $adjustment_values = isset($_POST['pbc_adjustment_value']) ? $_POST['pbc_adjustment_value'] : array();
        $is_active_values = isset($_POST['pbc_is_active']) ? $_POST['pbc_is_active'] : array();
        $rule_ids = isset($_POST['pbc_rule_id']) ? $_POST['pbc_rule_id'] : array();

        // Get existing rules to determine which ones to delete
        $existing_rules = $this->database->get_pricing_rules_by_type('product', $product_id, false);
        $existing_rule_ids = array();
        foreach ($existing_rules as $rule) {
            $existing_rule_ids[] = $rule->id;
        }

        $processed_rule_ids = array();

        // Process each submitted rule
        for ($i = 0; $i < count($country_codes); $i++) {
            if (empty($country_codes[$i]) || empty($adjustment_values[$i])) {
                continue;
            }

            $rule_data = array(
                'rule_type' => 'product',
                'object_id' => $product_id,
                'country_code' => sanitize_text_field($country_codes[$i]),
                'adjustment_type' => sanitize_text_field($adjustment_types[$i]),
                'adjustment_value' => floatval($adjustment_values[$i]),
                'is_active' => isset($is_active_values[$i]) ? 1 : 0
            );

            $rule_id = !empty($rule_ids[$i]) ? intval($rule_ids[$i]) : 0;

            if ($rule_id > 0) {
                // Update existing rule
                $this->database->update_pricing_rule($rule_id, $rule_data);
                $processed_rule_ids[] = $rule_id;
            } else {
                // Create new rule
                $new_rule_id = $this->database->create_pricing_rule($rule_data);
                if ($new_rule_id) {
                    $processed_rule_ids[] = $new_rule_id;
                }
            }
        }

        // Delete rules that were not processed (removed from form)
        $rules_to_delete = array_diff($existing_rule_ids, $processed_rule_ids);
        foreach ($rules_to_delete as $rule_id) {
            $this->database->delete_pricing_rule($rule_id);
        }

        // Clear product cache
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients($product_id);
        }
    }

    /**
     * Get countries options HTML
     *
     * @param string $selected_country Selected country code
     * @return string Options HTML
     */
    private function get_countries_options($selected_country = '') {
        $countries = WC()->countries->get_countries();
        $options = '';
        
        foreach ($countries as $code => $name) {
            $selected = selected($selected_country, $code, false);
            $options .= sprintf(
                '<option value="%s" %s>%s</option>',
                esc_attr($code),
                $selected,
                esc_html($name)
            );
        }
        
        return $options;
    }

    // ========================================
    // ADMIN SCRIPTS AND STYLES
    // ========================================

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page hook
     */
    public function enqueue_admin_scripts($hook) {
        // Check if we're on our plugin pages
        $plugin_pages = array('woocommerce_page_pbc-settings', 'woocommerce_page_pbc-dashboard', 'woocommerce_page_pbc-country-detection', 'woocommerce_page_pbc-import-export');
        $is_plugin_page = in_array($hook, $plugin_pages);
        
        // Only load on product edit pages, category pages, and our plugin pages
        if (!in_array($hook, array('post.php', 'post-new.php', 'edit-tags.php', 'term.php')) && !$is_plugin_page) {
            return;
        }

        global $post, $typenow;
        
        // Check if we're on a product page
        if (($hook === 'post.php' || $hook === 'post-new.php') && $typenow !== 'product' && !$is_plugin_page) {
            return;
        }

        // Check if we're on a product category page
        if (($hook === 'edit-tags.php' || $hook === 'term.php') && 
            (!isset($_GET['taxonomy']) || $_GET['taxonomy'] !== 'product_cat') && !$is_plugin_page) {
            return;
        }

        wp_enqueue_script(
            'pbc-admin',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/pbc-admin.js',
            array('jquery'),
            '1.0.1',
            true
        );

        wp_enqueue_style(
            'pbc-admin',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/pbc-admin.css',
            array(),
            '1.0.1'
        );

        // Localize script with AJAX URL and nonce
        wp_localize_script('pbc-admin', 'pbc_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pbc_admin_nonce'),
            'strings' => array(
                'confirm_delete' => __('Are you sure you want to delete this pricing rule?', 'price-by-country'),
                'select_country' => __('Please select a country', 'price-by-country'),
                'enter_value' => __('Please enter an adjustment value', 'price-by-country'),
                'saving' => __('Saving...', 'price-by-country'),
                'saved' => __('Saved', 'price-by-country'),
                'error' => __('Error saving rule', 'price-by-country')
            )
        ));
    }

    // ========================================
    // AJAX HANDLERS
    // ========================================

    /**
     * AJAX handler to get countries list
     */
    public function ajax_get_countries() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        $countries = WC()->countries->get_countries();
        wp_send_json_success($countries);
    }

    /**
     * AJAX handler to save product pricing rule
     */
    public function ajax_save_product_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('edit_products')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $product_id = intval($_POST['product_id']);
        $country_code = sanitize_text_field($_POST['country_code']);
        $adjustment_type = sanitize_text_field($_POST['adjustment_type']);
        $adjustment_value = floatval($_POST['adjustment_value']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $rule_id = isset($_POST['rule_id']) ? intval($_POST['rule_id']) : 0;

        // Validate input
        if (!$product_id || !$country_code || !$adjustment_value) {
            wp_send_json_error(__('Missing required fields', 'price-by-country'));
        }

        $rule_data = array(
            'rule_type' => 'product',
            'object_id' => $product_id,
            'country_code' => $country_code,
            'adjustment_type' => $adjustment_type,
            'adjustment_value' => $adjustment_value,
            'is_active' => $is_active
        );

        if ($rule_id > 0) {
            // Update existing rule
            $success = $this->database->update_pricing_rule($rule_id, $rule_data);
            $response_rule_id = $rule_id;
        } else {
            // Create new rule
            $response_rule_id = $this->database->create_pricing_rule($rule_data);
            $success = $response_rule_id !== false;
        }

        if ($success) {
            wp_send_json_success(array(
                'rule_id' => $response_rule_id,
                'message' => __('Rule saved successfully', 'price-by-country')
            ));
        } else {
            wp_send_json_error(__('Failed to save rule', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to delete product pricing rule
     */
    public function ajax_delete_product_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('edit_products')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);

        if (!$rule_id) {
            wp_send_json_error(__('Invalid rule ID', 'price-by-country'));
        }

        $success = $this->database->delete_pricing_rule($rule_id);

        if ($success) {
            wp_send_json_success(__('Rule deleted successfully', 'price-by-country'));
        } else {
            wp_send_json_error(__('Failed to delete rule', 'price-by-country'));
        }
    }

    // ========================================
    // CATEGORY-LEVEL PRICING INTERFACE
    // ========================================

    /**
     * Initialize category-level pricing hooks
     */
    private function init_category_hooks() {
        // Add category pricing fields
        add_action('product_cat_add_form_fields', array($this, 'add_category_pricing_fields'));
        add_action('product_cat_edit_form_fields', array($this, 'edit_category_pricing_fields'));
        
        // Save category pricing rules
        add_action('created_product_cat', array($this, 'save_category_pricing_rules'));
        add_action('edited_product_cat', array($this, 'save_category_pricing_rules'));
        
        // AJAX handlers for category pricing
        add_action('wp_ajax_pbc_bulk_apply_category_rules', array($this, 'ajax_bulk_apply_category_rules'));
        add_action('wp_ajax_pbc_save_category_rule', array($this, 'ajax_save_category_rule'));
        add_action('wp_ajax_pbc_delete_category_rule', array($this, 'ajax_delete_category_rule'));
    }

    /**
     * Add country pricing fields to new category form
     */
    public function add_category_pricing_fields() {
        ?>
        <div class="form-field pbc-category-pricing">
            <h3><?php _e('Country-Based Pricing', 'price-by-country'); ?></h3>
            <p class="description">
                <?php _e('Set different prices for this category. These rules will apply to all products in this category unless overridden at the product level.', 'price-by-country'); ?>
            </p>
            
            <div id="pbc-category-rules-container">
                <!-- Rules will be added here -->
            </div>
            
            <p>
                <button type="button" id="pbc-add-category-rule" class="button">
                    <?php _e('Add Country Pricing Rule', 'price-by-country'); ?>
                </button>
            </p>
            
            <div class="pbc-bulk-actions">
                <label>
                    <input type="checkbox" id="pbc-apply-to-existing" name="pbc_apply_to_existing" value="1" />
                    <?php _e('Apply these rules to existing products in this category', 'price-by-country'); ?>
                </label>
                <p class="description">
                    <?php _e('Check this to automatically apply category pricing rules to products that don\'t have their own country-specific pricing.', 'price-by-country'); ?>
                </p>
            </div>
        </div>

        <!-- Hidden template for new rules -->
        <script type="text/template" id="pbc-category-rule-template">
            <?php $this->render_category_rule_row(); ?>
        </script>
        <?php
    }

    /**
     * Add country pricing fields to edit category form
     *
     * @param object $term Category term object
     */
    public function edit_category_pricing_fields($term) {
        $category_id = $term->term_id;
        $existing_rules = $this->database->get_pricing_rules_by_type('category', $category_id);
        
        ?>
        <tr class="form-field pbc-category-pricing">
            <th scope="row">
                <label><?php _e('Country-Based Pricing', 'price-by-country'); ?></label>
            </th>
            <td>
                <div class="pbc-category-rules">
                    <p class="description">
                        <?php _e('Set different prices for this category. These rules will apply to all products in this category unless overridden at the product level.', 'price-by-country'); ?>
                    </p>
                    
                    <div id="pbc-category-rules-container">
                        <?php if (!empty($existing_rules)): ?>
                            <?php foreach ($existing_rules as $rule): ?>
                                <?php $this->render_category_rule_row($rule); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <p>
                        <button type="button" id="pbc-add-category-rule" class="button">
                            <?php _e('Add Country Pricing Rule', 'price-by-country'); ?>
                        </button>
                    </p>
                    
                    <div class="pbc-bulk-actions">
                        <label>
                            <input type="checkbox" id="pbc-apply-to-existing" name="pbc_apply_to_existing" value="1" />
                            <?php _e('Apply these rules to existing products in this category', 'price-by-country'); ?>
                        </label>
                        <button type="button" id="pbc-bulk-apply" class="button">
                            <?php _e('Apply to Products', 'price-by-country'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Apply category pricing rules to products that don\'t have their own country-specific pricing.', 'price-by-country'); ?>
                        </p>
                    </div>
                </div>
                
                <input type="hidden" id="pbc-category-id" value="<?php echo esc_attr($category_id); ?>" />
            </td>
        </tr>

        <!-- Hidden template for new rules -->
        <script type="text/template" id="pbc-category-rule-template">
            <?php $this->render_category_rule_row(); ?>
        </script>
        <?php
    }

    /**
     * Render a single category pricing rule row
     *
     * @param object|null $rule Existing rule data
     */
    private function render_category_rule_row($rule = null) {
        $rule_id = $rule ? $rule->id : '';
        $country_code = $rule ? $rule->country_code : '';
        $adjustment_type = $rule ? $rule->adjustment_type : 'percentage';
        $adjustment_value = $rule ? $rule->adjustment_value : '';
        $is_active = $rule ? $rule->is_active : 1;
        
        ?>
        <div class="pbc-rule-row" data-rule-id="<?php echo esc_attr($rule_id); ?>">
            <div class="pbc-rule-fields">
                <select name="pbc_category_country_code[]" class="pbc-country-select" required>
                    <option value=""><?php _e('Select Country', 'price-by-country'); ?></option>
                    <?php echo $this->get_countries_options($country_code); ?>
                </select>
                
                <select name="pbc_category_adjustment_type[]" class="pbc-adjustment-type">
                    <option value="percentage" <?php selected($adjustment_type, 'percentage'); ?>>
                        <?php _e('Percentage (%)', 'price-by-country'); ?>
                    </option>
                    <option value="fixed" <?php selected($adjustment_type, 'fixed'); ?>>
                        <?php _e('Fixed Amount', 'price-by-country'); ?>
                    </option>
                </select>
                
                <input type="number" 
                       name="pbc_category_adjustment_value[]" 
                       class="pbc-adjustment-value" 
                       value="<?php echo esc_attr($adjustment_value); ?>" 
                       step="0.01" 
                       placeholder="0.00" 
                       required />
                
                <label class="pbc-active-label">
                    <input type="checkbox" 
                           name="pbc_category_is_active[]" 
                           value="1" 
                           <?php checked($is_active, 1); ?> />
                    <?php _e('Active', 'price-by-country'); ?>
                </label>
                
                <button type="button" class="button pbc-remove-category-rule">
                    <?php _e('Remove', 'price-by-country'); ?>
                </button>
            </div>
            
            <input type="hidden" name="pbc_category_rule_id[]" value="<?php echo esc_attr($rule_id); ?>" />
        </div>
        <?php
    }

    /**
     * Save category pricing rules
     *
     * @param int $category_id Category ID
     */
    public function save_category_pricing_rules($category_id) {
        // Check user permissions
        if (!current_user_can('manage_product_terms')) {
            return;
        }

        // Get submitted rule data
        $country_codes = isset($_POST['pbc_category_country_code']) ? $_POST['pbc_category_country_code'] : array();
        $adjustment_types = isset($_POST['pbc_category_adjustment_type']) ? $_POST['pbc_category_adjustment_type'] : array();
        $adjustment_values = isset($_POST['pbc_category_adjustment_value']) ? $_POST['pbc_category_adjustment_value'] : array();
        $is_active_values = isset($_POST['pbc_category_is_active']) ? $_POST['pbc_category_is_active'] : array();
        $rule_ids = isset($_POST['pbc_category_rule_id']) ? $_POST['pbc_category_rule_id'] : array();
        $apply_to_existing = isset($_POST['pbc_apply_to_existing']) && $_POST['pbc_apply_to_existing'] === '1';

        // Get existing rules to determine which ones to delete
        $existing_rules = $this->database->get_pricing_rules_by_type('category', $category_id, false);
        $existing_rule_ids = array();
        foreach ($existing_rules as $rule) {
            $existing_rule_ids[] = $rule->id;
        }

        $processed_rule_ids = array();

        // Process each submitted rule
        for ($i = 0; $i < count($country_codes); $i++) {
            if (empty($country_codes[$i]) || empty($adjustment_values[$i])) {
                continue;
            }

            $rule_data = array(
                'rule_type' => 'category',
                'object_id' => $category_id,
                'country_code' => sanitize_text_field($country_codes[$i]),
                'adjustment_type' => sanitize_text_field($adjustment_types[$i]),
                'adjustment_value' => floatval($adjustment_values[$i]),
                'is_active' => isset($is_active_values[$i]) ? 1 : 0
            );

            $rule_id = !empty($rule_ids[$i]) ? intval($rule_ids[$i]) : 0;

            if ($rule_id > 0) {
                // Update existing rule
                $this->database->update_pricing_rule($rule_id, $rule_data);
                $processed_rule_ids[] = $rule_id;
            } else {
                // Create new rule
                $new_rule_id = $this->database->create_pricing_rule($rule_data);
                if ($new_rule_id) {
                    $processed_rule_ids[] = $new_rule_id;
                }
            }
        }

        // Delete rules that were not processed (removed from form)
        $rules_to_delete = array_diff($existing_rule_ids, $processed_rule_ids);
        foreach ($rules_to_delete as $rule_id) {
            $this->database->delete_pricing_rule($rule_id);
        }

        // Apply to existing products if requested
        if ($apply_to_existing && !empty($processed_rule_ids)) {
            $this->apply_category_rules_to_products($category_id);
        }
    }

    /**
     * Apply category pricing rules to products in the category
     *
     * @param int $category_id Category ID
     * @return int Number of products updated
     */
    private function apply_category_rules_to_products($category_id) {
        // Get products in this category
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                )
            ),
            'fields' => 'ids'
        ));

        $updated_count = 0;
        $category_rules = $this->database->get_pricing_rules_by_type('category', $category_id);

        foreach ($products as $product_id) {
            // Check if product already has its own pricing rules
            $existing_product_rules = $this->database->get_pricing_rules_by_type('product', $product_id);
            
            // Only apply category rules if product doesn't have its own rules
            if (empty($existing_product_rules)) {
                foreach ($category_rules as $category_rule) {
                    $product_rule_data = array(
                        'rule_type' => 'product',
                        'object_id' => $product_id,
                        'country_code' => $category_rule->country_code,
                        'adjustment_type' => $category_rule->adjustment_type,
                        'adjustment_value' => $category_rule->adjustment_value,
                        'is_active' => $category_rule->is_active
                    );
                    
                    $this->database->create_pricing_rule($product_rule_data);
                }
                $updated_count++;
            }
        }

        return $updated_count;
    }

    /**
     * AJAX handler to bulk apply category rules to products
     */
    public function ajax_bulk_apply_category_rules() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_product_terms')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $category_id = intval($_POST['category_id']);
        $apply_to_existing = isset($_POST['apply_to_existing']) && $_POST['apply_to_existing'] === '1';

        if (!$category_id) {
            wp_send_json_error(__('Invalid category ID', 'price-by-country'));
        }

        if ($apply_to_existing) {
            $updated_count = $this->apply_category_rules_to_products($category_id);
            wp_send_json_success(array(
                'count' => $updated_count,
                'message' => sprintf(__('Applied rules to %d products', 'price-by-country'), $updated_count)
            ));
        } else {
            wp_send_json_error(__('Apply to existing products option not selected', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to save category pricing rule
     */
    public function ajax_save_category_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_product_terms')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $category_id = intval($_POST['category_id']);
        $country_code = sanitize_text_field($_POST['country_code']);
        $adjustment_type = sanitize_text_field($_POST['adjustment_type']);
        $adjustment_value = floatval($_POST['adjustment_value']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $rule_id = isset($_POST['rule_id']) ? intval($_POST['rule_id']) : 0;

        // Validate input
        if (!$category_id || !$country_code || !$adjustment_value) {
            wp_send_json_error(__('Missing required fields', 'price-by-country'));
        }

        $rule_data = array(
            'rule_type' => 'category',
            'object_id' => $category_id,
            'country_code' => $country_code,
            'adjustment_type' => $adjustment_type,
            'adjustment_value' => $adjustment_value,
            'is_active' => $is_active
        );

        if ($rule_id > 0) {
            // Update existing rule
            $success = $this->database->update_pricing_rule($rule_id, $rule_data);
            $response_rule_id = $rule_id;
        } else {
            // Create new rule
            $response_rule_id = $this->database->create_pricing_rule($rule_data);
            $success = $response_rule_id !== false;
        }

        if ($success) {
            wp_send_json_success(array(
                'rule_id' => $response_rule_id,
                'message' => __('Category rule saved successfully', 'price-by-country')
            ));
        } else {
            wp_send_json_error(__('Failed to save category rule', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to delete category pricing rule
     */
    public function ajax_delete_category_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_product_terms')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);

        if (!$rule_id) {
            wp_send_json_error(__('Invalid rule ID', 'price-by-country'));
        }

        $success = $this->database->delete_pricing_rule($rule_id);

        if ($success) {
            wp_send_json_success(__('Category rule deleted successfully', 'price-by-country'));
        } else {
            wp_send_json_error(__('Failed to delete category rule', 'price-by-country'));
        }
    }

    // ========================================
    // GLOBAL PRICING SETTINGS INTERFACE
    // ========================================

    /**
     * Initialize global settings hooks
     */
    private function init_global_hooks() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu_wrapper'));
        
        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
        
        // Logs management AJAX handlers
        add_action('wp_ajax_pbc_get_logs', array($this, 'ajax_get_logs'));
        add_action('wp_ajax_pbc_clear_logs', array($this, 'ajax_clear_logs'));
        add_action('wp_ajax_pbc_export_logs', array($this, 'ajax_export_logs'));
        
        // AJAX handlers for global settings
        add_action('wp_ajax_pbc_save_global_settings', array($this, 'ajax_save_global_settings'));
    }

    /**
     * Wrapper for admin menu creation that handles translation loading
     */
    public function add_admin_menu_wrapper() {
        // If translations aren't loaded yet, delay menu creation
        if (did_action('init') === 0) {
            add_action('init', array($this, 'add_admin_menu'), 10);
            return;
        }
        
        $this->add_admin_menu();
    }

    /**
     * Add admin menu pages
     */
    public function add_admin_menu() {
        // Main settings page under WooCommerce
        add_submenu_page(
            'woocommerce',
            __('Price by Country Settings', 'price-by-country'),
            __('Price by Country', 'price-by-country'),
            'manage_woocommerce',
            'pbc-settings',
            array($this, 'render_global_settings_page')
        );

        // Dashboard page
        add_submenu_page(
            'woocommerce',
            __('Price by Country Dashboard', 'price-by-country'),
            __('Pricing Dashboard', 'price-by-country'),
            'manage_woocommerce',
            'pbc-dashboard',
            array($this, 'render_dashboard_page')
        );

        // Logs page
        add_submenu_page(
            'woocommerce',
            __('Price by Country Logs', 'price-by-country'),
            __('Pricing Logs', 'price-by-country'),
            'manage_woocommerce',
            'pbc-logs',
            array($this, 'render_logs_page')
        );

        // Country Detection Settings page
        add_submenu_page(
            'woocommerce',
            __('Country Detection Settings', 'price-by-country'),
            __('Country Detection', 'price-by-country'),
            'manage_woocommerce',
            'pbc-country-detection',
            array($this, 'render_country_detection_page')
        );

        // Import/Export page
        add_submenu_page(
            'woocommerce',
            __('Price by Country Import/Export', 'price-by-country'),
            __('Import/Export', 'price-by-country'),
            'manage_woocommerce',
            'pbc-import-export',
            array($this, 'render_import_export_page')
        );
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting('pbc_settings', 'pbc_global_rules');
        register_setting('pbc_settings', 'pbc_detection_method');
        register_setting('pbc_settings', 'pbc_detection_priority');
        register_setting('pbc_settings', 'pbc_cache_duration');
        register_setting('pbc_settings', 'pbc_fallback_country');
        register_setting('pbc_settings', 'pbc_enable_logging');
        
        // Country detection specific settings
        register_setting('pbc_country_detection_settings', 'pbc_detection_method');
        register_setting('pbc_country_detection_settings', 'pbc_detection_priority');
        register_setting('pbc_country_detection_settings', 'pbc_cache_duration');
        register_setting('pbc_country_detection_settings', 'pbc_fallback_country');
        register_setting('pbc_country_detection_settings', 'pbc_enable_logging');
        register_setting('pbc_country_detection_settings', 'pbc_ip_detection_service');
        register_setting('pbc_country_detection_settings', 'pbc_detection_accuracy_threshold');
    }

    /**
     * Render global settings page
     */
    public function render_global_settings_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        $global_rules = $this->database->get_pricing_rules_by_type('global');
        $detection_method = get_option('pbc_detection_method', 'auto');
        $detection_priority = get_option('pbc_detection_priority', array('shipping', 'billing', 'ip'));
        $cache_duration = get_option('pbc_cache_duration', 3600);
        $fallback_country = get_option('pbc_fallback_country', 'US');
        $enable_logging = get_option('pbc_enable_logging', 0);

        ?>
        <div class="wrap">
            <h1><?php _e('Price by Country Settings', 'price-by-country'); ?></h1>
            
            <form id="pbc-global-settings-form" method="post" action="options.php">
                <?php settings_fields('pbc_settings'); ?>
                
                <div class="pbc-global-settings">
                    
                    <!-- Global Pricing Rules Section -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Global Pricing Rules', 'price-by-country'); ?></h3>
                        <p class="description">
                            <?php _e('These rules apply to all products unless overridden by category or product-specific rules.', 'price-by-country'); ?>
                        </p>
                        
                        <div id="pbc-global-rules-container">
                            <?php if (!empty($global_rules)): ?>
                                <?php foreach ($global_rules as $rule): ?>
                                    <?php $this->render_global_rule_row($rule); ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <p>
                            <button type="button" id="pbc-add-global-rule" class="button">
                                <?php _e('Add Global Pricing Rule', 'price-by-country'); ?>
                            </button>
                        </p>
                    </div>

                    <!-- Country Detection Settings Section -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Country Detection Settings', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_detection_method"><?php _e('Detection Method', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <select name="pbc_detection_method" id="pbc_detection_method">
                                        <option value="auto" <?php selected($detection_method, 'auto'); ?>>
                                            <?php _e('Automatic (Priority Order)', 'price-by-country'); ?>
                                        </option>
                                        <option value="ip" <?php selected($detection_method, 'ip'); ?>>
                                            <?php _e('IP Address Only', 'price-by-country'); ?>
                                        </option>
                                        <option value="billing" <?php selected($detection_method, 'billing'); ?>>
                                            <?php _e('Billing Address Only', 'price-by-country'); ?>
                                        </option>
                                        <option value="shipping" <?php selected($detection_method, 'shipping'); ?>>
                                            <?php _e('Shipping Address Only', 'price-by-country'); ?>
                                        </option>
                                    </select>
                                    <p class="description">
                                        <?php _e('Choose how customer countries are detected.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="pbc_cache_duration"><?php _e('Cache Duration', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <input type="number" 
                                           name="pbc_cache_duration" 
                                           id="pbc_cache_duration" 
                                           value="<?php echo esc_attr($cache_duration); ?>" 
                                           min="300" 
                                           max="86400" 
                                           step="60" />
                                    <span><?php _e('seconds', 'price-by-country'); ?></span>
                                    <p class="description">
                                        <?php _e('How long to cache country detection results (300-86400 seconds).', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="pbc_fallback_country"><?php _e('Fallback Country', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <select name="pbc_fallback_country" id="pbc_fallback_country">
                                        <?php echo $this->get_countries_options($fallback_country); ?>
                                    </select>
                                    <p class="description">
                                        <?php _e('Default country to use when detection fails.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Rule Priority and Inheritance Section -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Rule Priority and Inheritance', 'price-by-country'); ?></h3>
                        
                        <table class="pbc-settings-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Priority', 'price-by-country'); ?></th>
                                    <th><?php _e('Rule Type', 'price-by-country'); ?></th>
                                    <th><?php _e('Description', 'price-by-country'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>1</strong></td>
                                    <td><?php _e('Product-Level', 'price-by-country'); ?></td>
                                    <td><?php _e('Highest priority. Overrides all other rules.', 'price-by-country'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>2</strong></td>
                                    <td><?php _e('Category-Level', 'price-by-country'); ?></td>
                                    <td><?php _e('Applied when no product-level rules exist.', 'price-by-country'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>3</strong></td>
                                    <td><?php _e('Global-Level', 'price-by-country'); ?></td>
                                    <td><?php _e('Lowest priority. Used as fallback for all products.', 'price-by-country'); ?></td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <p class="description">
                            <?php _e('Rules are applied in order of priority. Higher priority rules override lower priority ones.', 'price-by-country'); ?>
                        </p>
                    </div>

                    <!-- Advanced Settings Section -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Advanced Settings', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_enable_logging"><?php _e('Enable Logging', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox" 
                                               name="pbc_enable_logging" 
                                               id="pbc_enable_logging" 
                                               value="1" 
                                               <?php checked($enable_logging, 1); ?> />
                                        <?php _e('Enable debug logging for troubleshooting', 'price-by-country'); ?>
                                    </label>
                                    <p class="description">
                                        <?php _e('Logs pricing calculations and country detection for debugging purposes.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Import/Export Section -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Import/Export', 'price-by-country'); ?></h3>
                        
                        <p>
                            <button type="button" id="pbc-export-rules" class="button">
                                <?php _e('Export All Rules', 'price-by-country'); ?>
                            </button>
                            <button type="button" id="pbc-import-rules" class="button">
                                <?php _e('Import Rules', 'price-by-country'); ?>
                            </button>
                            <input type="file" id="pbc-import-file" accept=".csv" style="display: none;" />
                        </p>
                        
                        <p class="description">
                            <?php _e('Export your pricing rules to CSV or import rules from a CSV file.', 'price-by-country'); ?>
                        </p>
                    </div>

                    <!-- Save Button -->
                    <p class="submit">
                        <button type="button" id="pbc-save-global-settings" class="button-primary">
                            <?php _e('Save Settings', 'price-by-country'); ?>
                        </button>
                    </p>
                </div>
            </form>
        </div>

        <!-- Hidden template for new global rules -->
        <script type="text/template" id="pbc-global-rule-template">
            <?php $this->render_global_rule_row(); ?>
        </script>
        <?php
    }

    /**
     * Render a single global pricing rule row
     *
     * @param object|null $rule Existing rule data
     */
    private function render_global_rule_row($rule = null) {
        $rule_id = $rule ? $rule->id : '';
        $country_code = $rule ? $rule->country_code : '';
        $adjustment_type = $rule ? $rule->adjustment_type : 'percentage';
        $adjustment_value = $rule ? $rule->adjustment_value : '';
        $is_active = $rule ? $rule->is_active : 1;
        
        ?>
        <div class="pbc-rule-row" data-rule-id="<?php echo esc_attr($rule_id); ?>">
            <div class="pbc-rule-fields">
                <select name="pbc_global_country_code[]" class="pbc-country-select" required>
                    <option value=""><?php _e('Select Country', 'price-by-country'); ?></option>
                    <?php echo $this->get_countries_options($country_code); ?>
                </select>
                
                <select name="pbc_global_adjustment_type[]" class="pbc-adjustment-type">
                    <option value="percentage" <?php selected($adjustment_type, 'percentage'); ?>>
                        <?php _e('Percentage (%)', 'price-by-country'); ?>
                    </option>
                    <option value="fixed" <?php selected($adjustment_type, 'fixed'); ?>>
                        <?php _e('Fixed Amount', 'price-by-country'); ?>
                    </option>
                </select>
                
                <input type="number" 
                       name="pbc_global_adjustment_value[]" 
                       class="pbc-adjustment-value" 
                       value="<?php echo esc_attr($adjustment_value); ?>" 
                       step="0.01" 
                       placeholder="0.00" 
                       required />
                
                <label class="pbc-active-label">
                    <input type="checkbox" 
                           name="pbc_global_is_active[]" 
                           value="1" 
                           <?php checked($is_active, 1); ?> />
                    <?php _e('Active', 'price-by-country'); ?>
                </label>
                
                <button type="button" class="button pbc-remove-global-rule">
                    <?php _e('Remove', 'price-by-country'); ?>
                </button>
            </div>
            
            <input type="hidden" name="pbc_global_rule_id[]" value="<?php echo esc_attr($rule_id); ?>" />
        </div>
        <?php
    }

    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Handle search and filtering
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
        $rule_type_filter = isset($_GET['rule_type']) ? sanitize_text_field($_GET['rule_type']) : '';
        $country_filter = isset($_GET['country']) ? sanitize_text_field($_GET['country']) : '';
        $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
        $per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
        $current_page = isset($_GET['paged']) ? intval($_GET['paged']) : 1;

        // Get statistics
        $stats = $this->get_dashboard_stats();
        
        // Get filtered rules with pagination
        $rules_data = $this->get_filtered_rules(array(
            'search' => $search,
            'rule_type' => $rule_type_filter,
            'country' => $country_filter,
            'status' => $status_filter,
            'per_page' => $per_page,
            'page' => $current_page
        ));

        // Get unique countries for filter dropdown
        $countries_in_use = $this->database->get_countries_in_use();
        $all_countries = WC()->countries->get_countries();

        ?>
        <div class="wrap">
            <h1><?php _e('Price by Country Dashboard', 'price-by-country'); ?></h1>
            
            <!-- Statistics Cards -->
            <div class="pbc-stats-grid">
                <div class="pbc-stat-card">
                    <h3><?php _e('Total Rules', 'price-by-country'); ?></h3>
                    <p class="stat-number"><?php echo $stats['total_rules']; ?></p>
                </div>
                
                <div class="pbc-stat-card">
                    <h3><?php _e('Active Rules', 'price-by-country'); ?></h3>
                    <p class="stat-number active"><?php echo $stats['active_rules']; ?></p>
                </div>
                
                <div class="pbc-stat-card">
                    <h3><?php _e('Countries', 'price-by-country'); ?></h3>
                    <p class="stat-number"><?php echo $stats['countries_count']; ?></p>
                </div>
                
                <div class="pbc-stat-card">
                    <h3><?php _e('Conflicts', 'price-by-country'); ?></h3>
                    <p class="stat-number warning"><?php echo $stats['conflicts']; ?></p>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="pbc-filters-section">
                <form method="get" class="pbc-filters-form">
                    <input type="hidden" name="page" value="pbc-dashboard" />
                    
                    <div class="pbc-filter-row">
                        <div class="pbc-filter-group">
                            <label for="search"><?php _e('Search:', 'price-by-country'); ?></label>
                            <input type="text" 
                                   id="search" 
                                   name="search" 
                                   value="<?php echo esc_attr($search); ?>" 
                                   placeholder="<?php _e('Search rules...', 'price-by-country'); ?>" />
                        </div>
                        
                        <div class="pbc-filter-group">
                            <label for="rule_type"><?php _e('Type:', 'price-by-country'); ?></label>
                            <select id="rule_type" name="rule_type">
                                <option value=""><?php _e('All Types', 'price-by-country'); ?></option>
                                <option value="global" <?php selected($rule_type_filter, 'global'); ?>><?php _e('Global', 'price-by-country'); ?></option>
                                <option value="category" <?php selected($rule_type_filter, 'category'); ?>><?php _e('Category', 'price-by-country'); ?></option>
                                <option value="product" <?php selected($rule_type_filter, 'product'); ?>><?php _e('Product', 'price-by-country'); ?></option>
                            </select>
                        </div>
                        
                        <div class="pbc-filter-group">
                            <label for="country"><?php _e('Country:', 'price-by-country'); ?></label>
                            <select id="country" name="country">
                                <option value=""><?php _e('All Countries', 'price-by-country'); ?></option>
                                <?php foreach ($countries_in_use as $country_code): ?>
                                    <option value="<?php echo esc_attr($country_code); ?>" <?php selected($country_filter, $country_code); ?>>
                                        <?php echo isset($all_countries[$country_code]) ? $all_countries[$country_code] : $country_code; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="pbc-filter-group">
                            <label for="status"><?php _e('Status:', 'price-by-country'); ?></label>
                            <select id="status" name="status">
                                <option value=""><?php _e('All Status', 'price-by-country'); ?></option>
                                <option value="active" <?php selected($status_filter, 'active'); ?>><?php _e('Active', 'price-by-country'); ?></option>
                                <option value="inactive" <?php selected($status_filter, 'inactive'); ?>><?php _e('Inactive', 'price-by-country'); ?></option>
                            </select>
                        </div>
                        
                        <div class="pbc-filter-group">
                            <label for="per_page"><?php _e('Per Page:', 'price-by-country'); ?></label>
                            <select id="per_page" name="per_page">
                                <option value="20" <?php selected($per_page, 20); ?>>20</option>
                                <option value="50" <?php selected($per_page, 50); ?>>50</option>
                                <option value="100" <?php selected($per_page, 100); ?>>100</option>
                            </select>
                        </div>
                        
                        <div class="pbc-filter-actions">
                            <button type="submit" class="button"><?php _e('Filter', 'price-by-country'); ?></button>
                            <a href="<?php echo admin_url('admin.php?page=pbc-dashboard'); ?>" class="button"><?php _e('Clear', 'price-by-country'); ?></a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Rule Hierarchy Visualization -->
            <?php if ($stats['conflicts'] > 0): ?>
            <div class="pbc-conflicts-notice">
                <div class="notice notice-warning">
                    <p>
                        <strong><?php _e('Rule Conflicts Detected!', 'price-by-country'); ?></strong>
                        <?php printf(__('There are %d potential conflicts in your pricing rules.', 'price-by-country'), $stats['conflicts']); ?>
                        <a href="#" id="pbc-show-conflicts"><?php _e('View Details', 'price-by-country'); ?></a>
                    </p>
                </div>
                
                <div id="pbc-conflicts-details" style="display: none;">
                    <?php $this->render_conflicts_details(); ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Rules Table -->
            <div class="pbc-rules-table-section">
                <div class="pbc-table-header">
                    <h3><?php _e('Pricing Rules', 'price-by-country'); ?></h3>
                    <div class="pbc-bulk-actions">
                        <select id="pbc-bulk-action">
                            <option value=""><?php _e('Bulk Actions', 'price-by-country'); ?></option>
                            <option value="activate"><?php _e('Activate', 'price-by-country'); ?></option>
                            <option value="deactivate"><?php _e('Deactivate', 'price-by-country'); ?></option>
                            <option value="delete"><?php _e('Delete', 'price-by-country'); ?></option>
                        </select>
                        <button type="button" id="pbc-apply-bulk" class="button"><?php _e('Apply', 'price-by-country'); ?></button>
                    </div>
                </div>
                
                <?php if (!empty($rules_data['items'])): ?>
                    <table class="wp-list-table widefat fixed striped pbc-rules-table">
                        <thead>
                            <tr>
                                <td class="manage-column column-cb check-column">
                                    <input type="checkbox" id="pbc-select-all" />
                                </td>
                                <th class="manage-column column-type sortable">
                                    <a href="<?php echo $this->get_sort_url('rule_type'); ?>">
                                        <?php _e('Type', 'price-by-country'); ?>
                                        <span class="sorting-indicator"></span>
                                    </a>
                                </th>
                                <th class="manage-column column-object">
                                    <?php _e('Object', 'price-by-country'); ?>
                                </th>
                                <th class="manage-column column-country sortable">
                                    <a href="<?php echo $this->get_sort_url('country_code'); ?>">
                                        <?php _e('Country', 'price-by-country'); ?>
                                        <span class="sorting-indicator"></span>
                                    </a>
                                </th>
                                <th class="manage-column column-adjustment sortable">
                                    <a href="<?php echo $this->get_sort_url('adjustment_value'); ?>">
                                        <?php _e('Adjustment', 'price-by-country'); ?>
                                        <span class="sorting-indicator"></span>
                                    </a>
                                </th>
                                <th class="manage-column column-status">
                                    <?php _e('Status', 'price-by-country'); ?>
                                </th>
                                <th class="manage-column column-priority">
                                    <?php _e('Priority', 'price-by-country'); ?>
                                </th>
                                <th class="manage-column column-updated sortable">
                                    <a href="<?php echo $this->get_sort_url('updated_at'); ?>">
                                        <?php _e('Updated', 'price-by-country'); ?>
                                        <span class="sorting-indicator"></span>
                                    </a>
                                </th>
                                <th class="manage-column column-actions">
                                    <?php _e('Actions', 'price-by-country'); ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rules_data['items'] as $rule): ?>
                                <tr class="pbc-rule-row" data-rule-id="<?php echo $rule->id; ?>">
                                    <th class="check-column">
                                        <input type="checkbox" name="rule_ids[]" value="<?php echo $rule->id; ?>" />
                                    </th>
                                    <td class="column-type">
                                        <strong class="pbc-rule-type pbc-rule-type-<?php echo $rule->rule_type; ?>">
                                            <?php echo ucfirst($rule->rule_type); ?>
                                        </strong>
                                    </td>
                                    <td class="column-object">
                                        <?php echo $this->get_rule_object_display($rule); ?>
                                    </td>
                                    <td class="column-country">
                                        <span class="pbc-country-flag">
                                            <?php echo isset($all_countries[$rule->country_code]) ? $all_countries[$rule->country_code] : $rule->country_code; ?>
                                        </span>
                                        <small>(<?php echo $rule->country_code; ?>)</small>
                                    </td>
                                    <td class="column-adjustment">
                                        <span class="pbc-adjustment pbc-adjustment-<?php echo $rule->adjustment_type; ?>">
                                            <?php 
                                            if ($rule->adjustment_type === 'percentage') {
                                                echo $rule->adjustment_value . '%';
                                            } else {
                                                echo wc_price($rule->adjustment_value);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td class="column-status">
                                        <span class="pbc-status pbc-status-<?php echo $rule->is_active ? 'active' : 'inactive'; ?>">
                                            <?php echo $rule->is_active ? __('Active', 'price-by-country') : __('Inactive', 'price-by-country'); ?>
                                        </span>
                                    </td>
                                    <td class="column-priority">
                                        <span class="pbc-priority pbc-priority-<?php echo $this->get_rule_priority($rule->rule_type); ?>">
                                            <?php echo $this->get_rule_priority($rule->rule_type); ?>
                                        </span>
                                    </td>
                                    <td class="column-updated">
                                        <?php echo date_i18n(get_option('date_format'), strtotime($rule->updated_at)); ?>
                                        <br><small><?php echo date_i18n(get_option('time_format'), strtotime($rule->updated_at)); ?></small>
                                    </td>
                                    <td class="column-actions">
                                        <div class="row-actions">
                                            <span class="edit">
                                                <a href="#" class="pbc-edit-rule" data-rule-id="<?php echo $rule->id; ?>">
                                                    <?php _e('Edit', 'price-by-country'); ?>
                                                </a> |
                                            </span>
                                            <span class="toggle">
                                                <a href="#" class="pbc-toggle-rule" data-rule-id="<?php echo $rule->id; ?>" data-status="<?php echo $rule->is_active; ?>">
                                                    <?php echo $rule->is_active ? __('Deactivate', 'price-by-country') : __('Activate', 'price-by-country'); ?>
                                                </a> |
                                            </span>
                                            <span class="delete">
                                                <a href="#" class="pbc-delete-rule" data-rule-id="<?php echo $rule->id; ?>">
                                                    <?php _e('Delete', 'price-by-country'); ?>
                                                </a>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <?php if ($rules_data['total_pages'] > 1): ?>
                        <div class="pbc-pagination">
                            <?php
                            $pagination_args = array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo; Previous'),
                                'next_text' => __('Next &raquo;'),
                                'total' => $rules_data['total_pages'],
                                'current' => $current_page
                            );
                            echo paginate_links($pagination_args);
                            ?>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="pbc-no-rules">
                        <p><?php _e('No pricing rules found matching your criteria.', 'price-by-country'); ?></p>
                        <p>
                            <a href="<?php echo admin_url('admin.php?page=pbc-settings'); ?>" class="button button-primary">
                                <?php _e('Create Your First Rule', 'price-by-country'); ?>
                            </a>
                        </p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="pbc-quick-actions">
                <h3><?php _e('Quick Actions', 'price-by-country'); ?></h3>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=pbc-settings'); ?>" class="button button-primary">
                        <?php _e('Manage Global Settings', 'price-by-country'); ?>
                    </a>
                    <button type="button" id="pbc-cleanup-cache" class="button">
                        <?php _e('Clean Up Cache', 'price-by-country'); ?>
                    </button>
                    <button type="button" id="pbc-export-filtered" class="button">
                        <?php _e('Export Filtered Rules', 'price-by-country'); ?>
                    </button>
                    <button type="button" id="pbc-detect-conflicts" class="button">
                        <?php _e('Detect Conflicts', 'price-by-country'); ?>
                    </button>
                </p>
            </div>
        </div>

        <!-- Edit Rule Modal -->
        <div id="pbc-edit-rule-modal" class="pbc-modal" style="display: none;">
            <div class="pbc-modal-content">
                <div class="pbc-modal-header">
                    <h3><?php _e('Edit Pricing Rule', 'price-by-country'); ?></h3>
                    <button type="button" class="pbc-modal-close">&times;</button>
                </div>
                <div class="pbc-modal-body">
                    <form id="pbc-edit-rule-form">
                        <input type="hidden" id="edit-rule-id" name="rule_id" />
                        
                        <div class="pbc-form-row">
                            <label for="edit-country-code"><?php _e('Country:', 'price-by-country'); ?></label>
                            <select id="edit-country-code" name="country_code" required>
                                <?php echo $this->get_countries_options(); ?>
                            </select>
                        </div>
                        
                        <div class="pbc-form-row">
                            <label for="edit-adjustment-type"><?php _e('Adjustment Type:', 'price-by-country'); ?></label>
                            <select id="edit-adjustment-type" name="adjustment_type">
                                <option value="percentage"><?php _e('Percentage (%)', 'price-by-country'); ?></option>
                                <option value="fixed"><?php _e('Fixed Amount', 'price-by-country'); ?></option>
                            </select>
                        </div>
                        
                        <div class="pbc-form-row">
                            <label for="edit-adjustment-value"><?php _e('Adjustment Value:', 'price-by-country'); ?></label>
                            <input type="number" id="edit-adjustment-value" name="adjustment_value" step="0.01" required />
                        </div>
                        
                        <div class="pbc-form-row">
                            <label>
                                <input type="checkbox" id="edit-is-active" name="is_active" value="1" />
                                <?php _e('Active', 'price-by-country'); ?>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="pbc-modal-footer">
                    <button type="button" id="pbc-save-rule-edit" class="button button-primary">
                        <?php _e('Save Changes', 'price-by-country'); ?>
                    </button>
                    <button type="button" class="button pbc-modal-close">
                        <?php _e('Cancel', 'price-by-country'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX handler to save global settings
     */
    public function ajax_save_global_settings() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        // Save global pricing rules
        $this->save_global_pricing_rules();

        // Save other settings
        $detection_method = sanitize_text_field($_POST['pbc_detection_method']);
        $cache_duration = intval($_POST['pbc_cache_duration']);
        $fallback_country = sanitize_text_field($_POST['pbc_fallback_country']);
        $enable_logging = isset($_POST['pbc_enable_logging']) ? 1 : 0;

        update_option('pbc_detection_method', $detection_method);
        update_option('pbc_cache_duration', $cache_duration);
        update_option('pbc_fallback_country', $fallback_country);
        update_option('pbc_enable_logging', $enable_logging);

        wp_send_json_success(__('Global settings saved successfully', 'price-by-country'));
    }

    /**
     * Save global pricing rules
     */
    private function save_global_pricing_rules() {
        // Get submitted rule data
        $country_codes = isset($_POST['pbc_global_country_code']) ? $_POST['pbc_global_country_code'] : array();
        $adjustment_types = isset($_POST['pbc_global_adjustment_type']) ? $_POST['pbc_global_adjustment_type'] : array();
        $adjustment_values = isset($_POST['pbc_global_adjustment_value']) ? $_POST['pbc_global_adjustment_value'] : array();
        $is_active_values = isset($_POST['pbc_global_is_active']) ? $_POST['pbc_global_is_active'] : array();
        $rule_ids = isset($_POST['pbc_global_rule_id']) ? $_POST['pbc_global_rule_id'] : array();

        // Get existing rules to determine which ones to delete
        $existing_rules = $this->database->get_pricing_rules_by_type('global', null, false);
        $existing_rule_ids = array();
        foreach ($existing_rules as $rule) {
            $existing_rule_ids[] = $rule->id;
        }

        $processed_rule_ids = array();

        // Process each submitted rule
        for ($i = 0; $i < count($country_codes); $i++) {
            if (empty($country_codes[$i]) || empty($adjustment_values[$i])) {
                continue;
            }

            $rule_data = array(
                'rule_type' => 'global',
                'object_id' => null,
                'country_code' => sanitize_text_field($country_codes[$i]),
                'adjustment_type' => sanitize_text_field($adjustment_types[$i]),
                'adjustment_value' => floatval($adjustment_values[$i]),
                'is_active' => isset($is_active_values[$i]) ? 1 : 0
            );

            $rule_id = !empty($rule_ids[$i]) ? intval($rule_ids[$i]) : 0;

            if ($rule_id > 0) {
                // Update existing rule
                $this->database->update_pricing_rule($rule_id, $rule_data);
                $processed_rule_ids[] = $rule_id;
            } else {
                // Create new rule
                $new_rule_id = $this->database->create_pricing_rule($rule_data);
                if ($new_rule_id) {
                    $processed_rule_ids[] = $new_rule_id;
                }
            }
        }

        // Delete rules that were not processed (removed from form)
        $rules_to_delete = array_diff($existing_rule_ids, $processed_rule_ids);
        foreach ($rules_to_delete as $rule_id) {
            $this->database->delete_pricing_rule($rule_id);
        }
    }



    // ========================================
    // DASHBOARD HELPER METHODS
    // ========================================

    /**
     * Get dashboard statistics
     *
     * @return array Statistics data
     */
    private function get_dashboard_stats() {
        $stats = $this->database->get_stats();
        
        // Get conflict count
        $conflicts = $this->detect_rule_conflicts();
        
        return array(
            'total_rules' => $stats['pricing_rules'],
            'active_rules' => $this->database->get_active_rules_count(),
            'countries_count' => count($this->database->get_countries_in_use()),
            'conflicts' => count($conflicts)
        );
    }

    /**
     * Get filtered rules with pagination
     *
     * @param array $filters Filter parameters
     * @return array Rules data with pagination info
     */
    private function get_filtered_rules($filters) {
        return $this->database->get_pricing_rules_paginated($filters);
    }

    /**
     * Get rule object display text
     *
     * @param object $rule Rule object
     * @return string Display text
     */
    private function get_rule_object_display($rule) {
        if ($rule->rule_type === 'global') {
            return '<em>' . __('All Products', 'price-by-country') . '</em>';
        } elseif ($rule->rule_type === 'category' && $rule->object_id) {
            $term = get_term($rule->object_id, 'product_cat');
            if ($term && !is_wp_error($term)) {
                return '<a href="' . admin_url('term.php?taxonomy=product_cat&tag_ID=' . $rule->object_id) . '">' . $term->name . '</a>';
            }
            return __('Category ID: ', 'price-by-country') . $rule->object_id;
        } elseif ($rule->rule_type === 'product' && $rule->object_id) {
            $product = wc_get_product($rule->object_id);
            if ($product) {
                return '<a href="' . admin_url('post.php?post=' . $rule->object_id . '&action=edit') . '">' . $product->get_name() . '</a>';
            }
            return __('Product ID: ', 'price-by-country') . $rule->object_id;
        }
        
        return '-';
    }

    /**
     * Get rule priority number
     *
     * @param string $rule_type Rule type
     * @return int Priority number
     */
    private function get_rule_priority($rule_type) {
        switch ($rule_type) {
            case 'product':
                return 1;
            case 'category':
                return 2;
            case 'global':
                return 3;
            default:
                return 4;
        }
    }

    /**
     * Get sort URL for table columns
     *
     * @param string $column Column name
     * @return string Sort URL
     */
    private function get_sort_url($column) {
        $current_order = isset($_GET['order']) ? $_GET['order'] : 'desc';
        $current_orderby = isset($_GET['orderby']) ? $_GET['orderby'] : '';
        
        // Toggle order if same column
        $new_order = ($current_orderby === $column && $current_order === 'asc') ? 'desc' : 'asc';
        
        $args = array_merge($_GET, array(
            'orderby' => $column,
            'order' => $new_order
        ));
        
        return admin_url('admin.php?' . http_build_query($args));
    }

    /**
     * Detect rule conflicts
     *
     * @return array Array of conflicts
     */
    private function detect_rule_conflicts() {
        $conflicts = array();
        
        // Get all active rules grouped by country and object
        $rules_by_country = $this->database->get_rules_grouped_by_country();
        
        foreach ($rules_by_country as $country_code => $country_rules) {
            // Check for multiple rules affecting the same product
            $product_rules = array();
            
            foreach ($country_rules as $rule) {
                if ($rule->rule_type === 'product') {
                    $product_rules[$rule->object_id][] = $rule;
                } elseif ($rule->rule_type === 'category') {
                    // Get products in this category
                    $products = get_posts(array(
                        'post_type' => 'product',
                        'posts_per_page' => -1,
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'product_cat',
                                'field' => 'term_id',
                                'terms' => $rule->object_id
                            )
                        ),
                        'fields' => 'ids'
                    ));
                    
                    foreach ($products as $product_id) {
                        $product_rules[$product_id][] = $rule;
                    }
                } elseif ($rule->rule_type === 'global') {
                    // Global rules affect all products
                    // We'll check this separately
                }
            }
            
            // Find products with multiple rules
            foreach ($product_rules as $product_id => $rules) {
                if (count($rules) > 1) {
                    $conflicts[] = array(
                        'type' => 'multiple_rules',
                        'country' => $country_code,
                        'product_id' => $product_id,
                        'rules' => $rules
                    );
                }
            }
        }
        
        return $conflicts;
    }

    /**
     * Render conflicts details
     */
    private function render_conflicts_details() {
        $conflicts = $this->detect_rule_conflicts();
        
        if (empty($conflicts)) {
            echo '<p>' . __('No conflicts detected.', 'price-by-country') . '</p>';
            return;
        }
        
        ?>
        <div class="pbc-conflicts-list">
            <h4><?php _e('Detected Conflicts:', 'price-by-country'); ?></h4>
            
            <?php foreach ($conflicts as $conflict): ?>
                <div class="pbc-conflict-item">
                    <strong><?php _e('Product ID:', 'price-by-country'); ?> <?php echo $conflict['product_id']; ?></strong>
                    <span><?php _e('Country:', 'price-by-country'); ?> <?php echo $conflict['country']; ?></span>
                    
                    <ul class="pbc-conflict-rules">
                        <?php foreach ($conflict['rules'] as $rule): ?>
                            <li>
                                <?php echo ucfirst($rule->rule_type); ?> rule: 
                                <?php 
                                if ($rule->adjustment_type === 'percentage') {
                                    echo $rule->adjustment_value . '%';
                                } else {
                                    echo wc_price($rule->adjustment_value);
                                }
                                ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }

    // ========================================
    // DASHBOARD AJAX HANDLERS
    // ========================================

    /**
     * AJAX handler to toggle rule status
     */
    public function ajax_toggle_rule_status() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);
        $new_status = intval($_POST['status']);

        if (!$rule_id) {
            wp_send_json_error(__('Invalid rule ID', 'price-by-country'));
        }

        $success = $this->database->update_pricing_rule($rule_id, array('is_active' => $new_status));

        if ($success) {
            wp_send_json_success(array(
                'message' => $new_status ? __('Rule activated', 'price-by-country') : __('Rule deactivated', 'price-by-country'),
                'new_status' => $new_status
            ));
        } else {
            wp_send_json_error(__('Failed to update rule status', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to delete rule
     */
    public function ajax_delete_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);

        if (!$rule_id) {
            wp_send_json_error(__('Invalid rule ID', 'price-by-country'));
        }

        $success = $this->database->delete_pricing_rule($rule_id);

        if ($success) {
            wp_send_json_success(__('Rule deleted successfully', 'price-by-country'));
        } else {
            wp_send_json_error(__('Failed to delete rule', 'price-by-country'));
        }
    }

    /**
     * AJAX handler for bulk actions
     */
    public function ajax_bulk_action_rules() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $action = sanitize_text_field($_POST['action']);
        $rule_ids = array_map('intval', $_POST['rule_ids']);

        if (empty($rule_ids)) {
            wp_send_json_error(__('No rules selected', 'price-by-country'));
        }

        $success_count = 0;

        foreach ($rule_ids as $rule_id) {
            switch ($action) {
                case 'activate':
                    if ($this->database->update_pricing_rule($rule_id, array('is_active' => 1))) {
                        $success_count++;
                    }
                    break;
                case 'deactivate':
                    if ($this->database->update_pricing_rule($rule_id, array('is_active' => 0))) {
                        $success_count++;
                    }
                    break;
                case 'delete':
                    if ($this->database->delete_pricing_rule($rule_id)) {
                        $success_count++;
                    }
                    break;
            }
        }

        wp_send_json_success(array(
            'message' => sprintf(__('Successfully processed %d of %d rules', 'price-by-country'), $success_count, count($rule_ids)),
            'success_count' => $success_count,
            'total_count' => count($rule_ids)
        ));
    }

    /**
     * AJAX handler to edit rule
     */
    public function ajax_edit_rule() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);
        $country_code = sanitize_text_field($_POST['country_code']);
        $adjustment_type = sanitize_text_field($_POST['adjustment_type']);
        $adjustment_value = floatval($_POST['adjustment_value']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (!$rule_id || !$country_code || !$adjustment_value) {
            wp_send_json_error(__('Missing required fields', 'price-by-country'));
        }

        $rule_data = array(
            'country_code' => $country_code,
            'adjustment_type' => $adjustment_type,
            'adjustment_value' => $adjustment_value,
            'is_active' => $is_active
        );

        $success = $this->database->update_pricing_rule($rule_id, $rule_data);

        if ($success) {
            wp_send_json_success(__('Rule updated successfully', 'price-by-country'));
        } else {
            wp_send_json_error(__('Failed to update rule', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to get rule data
     */
    public function ajax_get_rule_data() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rule_id = intval($_POST['rule_id']);

        if (!$rule_id) {
            wp_send_json_error(__('Invalid rule ID', 'price-by-country'));
        }

        $rule = $this->database->get_pricing_rule($rule_id);

        if ($rule) {
            wp_send_json_success($rule);
        } else {
            wp_send_json_error(__('Rule not found', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to cleanup cache
     */
    public function ajax_cleanup_cache() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $cleaned = $this->database->cleanup_expired_cache();

        wp_send_json_success(array(
            'message' => sprintf(__('Cleaned up %d expired cache entries', 'price-by-country'), $cleaned),
            'cleaned_count' => $cleaned
        ));
    }

    /**
     * AJAX handler to detect conflicts
     */
    public function ajax_detect_conflicts() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $conflicts = $this->detect_rule_conflicts();

        wp_send_json_success(array(
            'conflicts' => $conflicts,
            'count' => count($conflicts),
            'message' => sprintf(__('Found %d potential conflicts', 'price-by-country'), count($conflicts))
        ));
    }

    // ========================================
    // COUNTRY DETECTION SETTINGS PAGE
    // ========================================

    /**
     * Render country detection settings page
     */
    public function render_country_detection_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Get current settings
        $detection_method = get_option('pbc_detection_method', 'auto');
        $detection_priority = get_option('pbc_detection_priority', array('shipping', 'billing', 'ip'));
        $cache_duration = get_option('pbc_cache_duration', 3600);
        $fallback_country = get_option('pbc_fallback_country', 'US');
        $enable_logging = get_option('pbc_enable_logging', 0);
        $ip_detection_service = get_option('pbc_ip_detection_service', 'woocommerce');
        $detection_accuracy_threshold = get_option('pbc_detection_accuracy_threshold', 0.8);

        // Get detection statistics
        $detection_stats = $this->get_detection_statistics();

        ?>
        <div class="wrap">
            <h1><?php _e('Country Detection Settings', 'price-by-country'); ?></h1>
            
            <form id="pbc-country-detection-form" method="post" action="options.php">
                <?php settings_fields('pbc_country_detection_settings'); ?>
                
                <div class="pbc-detection-settings">
                    
                    <!-- Detection Method Configuration -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Detection Method Configuration', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_detection_method"><?php _e('Primary Detection Method', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <select name="pbc_detection_method" id="pbc_detection_method">
                                        <option value="auto" <?php selected($detection_method, 'auto'); ?>>
                                            <?php _e('Automatic (Use Priority Order)', 'price-by-country'); ?>
                                        </option>
                                        <option value="ip" <?php selected($detection_method, 'ip'); ?>>
                                            <?php _e('IP Address Only', 'price-by-country'); ?>
                                        </option>
                                        <option value="billing" <?php selected($detection_method, 'billing'); ?>>
                                            <?php _e('Billing Address Only', 'price-by-country'); ?>
                                        </option>
                                        <option value="shipping" <?php selected($detection_method, 'shipping'); ?>>
                                            <?php _e('Shipping Address Only', 'price-by-country'); ?>
                                        </option>
                                    </select>
                                    <p class="description">
                                        <?php _e('Choose the primary method for detecting customer countries.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr id="pbc-priority-settings" <?php echo $detection_method !== 'auto' ? 'style="display:none;"' : ''; ?>>
                                <th scope="row">
                                    <label><?php _e('Detection Priority Order', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <div class="pbc-priority-list">
                                        <?php 
                                        $priority_labels = array(
                                            'shipping' => __('Shipping Address', 'price-by-country'),
                                            'billing' => __('Billing Address', 'price-by-country'),
                                            'ip' => __('IP Address', 'price-by-country')
                                        );
                                        
                                        foreach ($detection_priority as $index => $method): ?>
                                            <div class="pbc-priority-item" data-method="<?php echo esc_attr($method); ?>">
                                                <span class="pbc-priority-number"><?php echo $index + 1; ?></span>
                                                <span class="pbc-priority-label"><?php echo $priority_labels[$method]; ?></span>
                                                <div class="pbc-priority-controls">
                                                    <button type="button" class="button pbc-move-up" <?php echo $index === 0 ? 'disabled' : ''; ?>>↑</button>
                                                    <button type="button" class="button pbc-move-down" <?php echo $index === count($detection_priority) - 1 ? 'disabled' : ''; ?>>↓</button>
                                                </div>
                                                <input type="hidden" name="pbc_detection_priority[]" value="<?php echo esc_attr($method); ?>" />
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <p class="description">
                                        <?php _e('Drag or use arrows to reorder detection methods by priority. Higher priority methods are tried first.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- IP Detection Configuration -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('IP Detection Configuration', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_ip_detection_service"><?php _e('IP Detection Service', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <select name="pbc_ip_detection_service" id="pbc_ip_detection_service">
                                        <option value="woocommerce" <?php selected($ip_detection_service, 'woocommerce'); ?>>
                                            <?php _e('WooCommerce Built-in', 'price-by-country'); ?>
                                        </option>
                                        <option value="maxmind" <?php selected($ip_detection_service, 'maxmind'); ?>>
                                            <?php _e('MaxMind GeoIP2', 'price-by-country'); ?>
                                        </option>
                                        <option value="ipapi" <?php selected($ip_detection_service, 'ipapi'); ?>>
                                            <?php _e('IP-API.com', 'price-by-country'); ?>
                                        </option>
                                        <option value="ipstack" <?php selected($ip_detection_service, 'ipstack'); ?>>
                                            <?php _e('IPStack', 'price-by-country'); ?>
                                        </option>
                                    </select>
                                    <p class="description">
                                        <?php _e('Choose the service for IP-based country detection.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="pbc_detection_accuracy_threshold"><?php _e('Accuracy Threshold', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <input type="number" 
                                           name="pbc_detection_accuracy_threshold" 
                                           id="pbc_detection_accuracy_threshold" 
                                           value="<?php echo esc_attr($detection_accuracy_threshold); ?>" 
                                           min="0.1" 
                                           max="1.0" 
                                           step="0.1" />
                                    <p class="description">
                                        <?php _e('Minimum confidence level required for IP detection (0.1 - 1.0). Lower values are less strict.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Caching and Performance -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Caching and Performance', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_cache_duration"><?php _e('Cache Duration', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <input type="number" 
                                           name="pbc_cache_duration" 
                                           id="pbc_cache_duration" 
                                           value="<?php echo esc_attr($cache_duration); ?>" 
                                           min="300" 
                                           max="86400" 
                                           step="60" />
                                    <span><?php _e('seconds', 'price-by-country'); ?></span>
                                    <p class="description">
                                        <?php _e('How long to cache country detection results (300-86400 seconds).', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="pbc_fallback_country"><?php _e('Fallback Country', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <select name="pbc_fallback_country" id="pbc_fallback_country">
                                        <?php echo $this->get_countries_options($fallback_country); ?>
                                    </select>
                                    <p class="description">
                                        <?php _e('Default country to use when all detection methods fail.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Testing and Validation -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Testing and Validation', 'price-by-country'); ?></h3>
                        
                        <div class="pbc-test-section">
                            <h4><?php _e('Test Country Detection', 'price-by-country'); ?></h4>
                            <p class="description">
                                <?php _e('Test the country detection with different IP addresses or user data.', 'price-by-country'); ?>
                            </p>
                            
                            <div class="pbc-test-controls">
                                <div class="pbc-test-input-group">
                                    <label for="pbc_test_ip"><?php _e('Test IP Address:', 'price-by-country'); ?></label>
                                    <input type="text" id="pbc_test_ip" placeholder="*******" />
                                    <button type="button" id="pbc_test_ip_detection" class="button">
                                        <?php _e('Test IP Detection', 'price-by-country'); ?>
                                    </button>
                                </div>
                                
                                <div class="pbc-test-input-group">
                                    <label for="pbc_test_user_id"><?php _e('Test User ID:', 'price-by-country'); ?></label>
                                    <input type="number" id="pbc_test_user_id" placeholder="1" />
                                    <button type="button" id="pbc_test_user_detection" class="button">
                                        <?php _e('Test User Detection', 'price-by-country'); ?>
                                    </button>
                                </div>
                            </div>
                            
                            <div id="pbc_test_results" class="pbc-test-results" style="display: none;">
                                <h5><?php _e('Test Results:', 'price-by-country'); ?></h5>
                                <div id="pbc_test_output"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Detection Statistics -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Detection Statistics', 'price-by-country'); ?></h3>
                        
                        <div class="pbc-stats-grid">
                            <div class="pbc-stat-card">
                                <h4><?php _e('Total Detections', 'price-by-country'); ?></h4>
                                <p class="stat-number"><?php echo $detection_stats['total_detections']; ?></p>
                            </div>
                            
                            <div class="pbc-stat-card">
                                <h4><?php _e('IP Detections', 'price-by-country'); ?></h4>
                                <p class="stat-number"><?php echo $detection_stats['ip_detections']; ?></p>
                                <small><?php echo $detection_stats['ip_percentage']; ?>%</small>
                            </div>
                            
                            <div class="pbc-stat-card">
                                <h4><?php _e('Address Detections', 'price-by-country'); ?></h4>
                                <p class="stat-number"><?php echo $detection_stats['address_detections']; ?></p>
                                <small><?php echo $detection_stats['address_percentage']; ?>%</small>
                            </div>
                            
                            <div class="pbc-stat-card">
                                <h4><?php _e('Cache Hit Rate', 'price-by-country'); ?></h4>
                                <p class="stat-number"><?php echo $detection_stats['cache_hit_rate']; ?>%</p>
                            </div>
                        </div>
                        
                        <div class="pbc-detection-chart">
                            <h4><?php _e('Detection Methods Usage (Last 30 Days)', 'price-by-country'); ?></h4>
                            <canvas id="pbc_detection_chart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Advanced Settings -->
                    <div class="pbc-settings-section">
                        <h3><?php _e('Advanced Settings', 'price-by-country'); ?></h3>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="pbc_enable_logging"><?php _e('Enable Detection Logging', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox" 
                                               name="pbc_enable_logging" 
                                               id="pbc_enable_logging" 
                                               value="1" 
                                               <?php checked($enable_logging, 1); ?> />
                                        <?php _e('Log country detection attempts for debugging', 'price-by-country'); ?>
                                    </label>
                                    <p class="description">
                                        <?php _e('Enable detailed logging of country detection processes. Useful for troubleshooting but may impact performance.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Detection Log Viewer', 'price-by-country'); ?></label>
                                </th>
                                <td>
                                    <button type="button" id="pbc_view_detection_logs" class="button">
                                        <?php _e('View Detection Logs', 'price-by-country'); ?>
                                    </button>
                                    <button type="button" id="pbc_clear_detection_logs" class="button">
                                        <?php _e('Clear Logs', 'price-by-country'); ?>
                                    </button>
                                    <p class="description">
                                        <?php _e('View or clear the country detection logs.', 'price-by-country'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Save Button -->
                    <p class="submit">
                        <button type="button" id="pbc-save-detection-settings" class="button-primary">
                            <?php _e('Save Detection Settings', 'price-by-country'); ?>
                        </button>
                        <button type="button" id="pbc-reset-detection-settings" class="button">
                            <?php _e('Reset to Defaults', 'price-by-country'); ?>
                        </button>
                    </p>
                </div>
            </form>
        </div>

        <!-- Detection Log Modal -->
        <div id="pbc-detection-log-modal" class="pbc-modal" style="display: none;">
            <div class="pbc-modal-content" style="max-width: 800px;">
                <div class="pbc-modal-header">
                    <h3><?php _e('Country Detection Logs', 'price-by-country'); ?></h3>
                    <button type="button" class="pbc-modal-close">&times;</button>
                </div>
                <div class="pbc-modal-body">
                    <div id="pbc-detection-log-content">
                        <p><?php _e('Loading logs...', 'price-by-country'); ?></p>
                    </div>
                </div>
                <div class="pbc-modal-footer">
                    <button type="button" class="button pbc-modal-close">
                        <?php _e('Close', 'price-by-country'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get detection statistics
     *
     * @return array Detection statistics
     */
    private function get_detection_statistics() {
        $total_detections = $this->database->get_detection_count();
        $ip_detections = $this->database->get_detection_count('ip');
        $billing_detections = $this->database->get_detection_count('billing');
        $shipping_detections = $this->database->get_detection_count('shipping');
        $address_detections = $billing_detections + $shipping_detections;
        
        $cache_stats = $this->database->get_cache_statistics();
        
        return array(
            'total_detections' => $total_detections,
            'ip_detections' => $ip_detections,
            'address_detections' => $address_detections,
            'ip_percentage' => $total_detections > 0 ? round(($ip_detections / $total_detections) * 100, 1) : 0,
            'address_percentage' => $total_detections > 0 ? round(($address_detections / $total_detections) * 100, 1) : 0,
            'cache_hit_rate' => isset($cache_stats['hit_rate']) ? $cache_stats['hit_rate'] : 0
        );
    }

    // ========================================
    // COUNTRY DETECTION AJAX HANDLERS
    // ========================================

    /**
     * AJAX handler to save country detection settings
     */
    public function ajax_save_detection_settings() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        // Save detection settings
        $detection_method = sanitize_text_field($_POST['pbc_detection_method']);
        $detection_priority = isset($_POST['pbc_detection_priority']) ? array_map('sanitize_text_field', $_POST['pbc_detection_priority']) : array();
        $cache_duration = intval($_POST['pbc_cache_duration']);
        $fallback_country = sanitize_text_field($_POST['pbc_fallback_country']);
        $enable_logging = isset($_POST['pbc_enable_logging']) ? 1 : 0;
        $ip_detection_service = sanitize_text_field($_POST['pbc_ip_detection_service']);
        $detection_accuracy_threshold = floatval($_POST['pbc_detection_accuracy_threshold']);

        update_option('pbc_detection_method', $detection_method);
        update_option('pbc_detection_priority', $detection_priority);
        update_option('pbc_cache_duration', $cache_duration);
        update_option('pbc_fallback_country', $fallback_country);
        update_option('pbc_enable_logging', $enable_logging);
        update_option('pbc_ip_detection_service', $ip_detection_service);
        update_option('pbc_detection_accuracy_threshold', $detection_accuracy_threshold);

        wp_send_json_success(__('Detection settings saved successfully', 'price-by-country'));
    }

    /**
     * AJAX handler to test IP detection
     */
    public function ajax_test_ip_detection() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $test_ip = sanitize_text_field($_POST['test_ip']);
        
        if (!filter_var($test_ip, FILTER_VALIDATE_IP)) {
            wp_send_json_error(__('Invalid IP address', 'price-by-country'));
        }

        // Use country detector to test IP
        $country_detector = new PBC_Country_Detector($this->database);
        $result = $country_detector->get_country_from_ip($test_ip);

        if ($result) {
            $countries = WC()->countries->get_countries();
            $country_name = isset($countries[$result]) ? $countries[$result] : $result;
            
            wp_send_json_success(array(
                'country_code' => $result,
                'country_name' => $country_name,
                'ip_address' => $test_ip,
                'detection_method' => 'ip',
                'message' => sprintf(__('IP %s detected as %s (%s)', 'price-by-country'), $test_ip, $country_name, $result)
            ));
        } else {
            wp_send_json_error(__('Could not detect country for this IP address', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to test user detection
     */
    public function ajax_test_user_detection() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $user_id = intval($_POST['user_id']);
        
        if (!$user_id || !get_user_by('id', $user_id)) {
            wp_send_json_error(__('Invalid user ID', 'price-by-country'));
        }

        // Use country detector to test user detection
        $country_detector = new PBC_Country_Detector($this->database);
        
        $billing_country = $country_detector->get_country_from_billing($user_id);
        $shipping_country = $country_detector->get_country_from_shipping($user_id);
        
        $countries = WC()->countries->get_countries();
        
        $results = array();
        
        if ($billing_country) {
            $results[] = array(
                'method' => 'billing',
                'country_code' => $billing_country,
                'country_name' => isset($countries[$billing_country]) ? $countries[$billing_country] : $billing_country
            );
        }
        
        if ($shipping_country) {
            $results[] = array(
                'method' => 'shipping',
                'country_code' => $shipping_country,
                'country_name' => isset($countries[$shipping_country]) ? $countries[$shipping_country] : $shipping_country
            );
        }

        if (!empty($results)) {
            wp_send_json_success(array(
                'user_id' => $user_id,
                'results' => $results,
                'message' => sprintf(__('Found %d detection method(s) for user %d', 'price-by-country'), count($results), $user_id)
            ));
        } else {
            wp_send_json_error(__('No country information found for this user', 'price-by-country'));
        }
    }

    /**
     * AJAX handler to view detection logs
     */
    public function ajax_view_detection_logs() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $logs = $this->get_detection_logs();
        
        wp_send_json_success(array(
            'logs' => $logs,
            'count' => count($logs)
        ));
    }

    /**
     * AJAX handler to clear detection logs
     */
    public function ajax_clear_detection_logs() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $cleared = $this->clear_detection_logs();
        
        wp_send_json_success(array(
            'message' => sprintf(__('Cleared %d log entries', 'price-by-country'), $cleared),
            'cleared_count' => $cleared
        ));
    }

    /**
     * AJAX handler to clear all logs
     */
    public function ajax_clear_logs() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $logger = PBC_Logger::get_instance();
        $cleared = $logger->clear_logs();
        
        wp_send_json_success(array(
            'message' => sprintf(__('Cleared %d log entries', 'price-by-country'), $cleared),
            'cleared_count' => $cleared
        ));
    }

    /**
     * Get detection logs
     *
     * @return array Detection logs
     */
    private function get_detection_logs() {
        // This would typically read from a log file or database table
        // For now, return sample data
        return array(
            array(
                'timestamp' => current_time('mysql'),
                'ip_address' => '***********',
                'method' => 'ip',
                'country' => 'US',
                'success' => true,
                'message' => 'Successfully detected country from IP'
            ),
            array(
                'timestamp' => current_time('mysql'),
                'ip_address' => '********',
                'method' => 'billing',
                'country' => 'CA',
                'success' => true,
                'message' => 'Used billing address for detection'
            )
        );
    }

    /**
     * Clear detection logs
     *
     * @return int Number of cleared entries
     */
    private function clear_detection_logs() {
        // This would typically clear log files or database entries
        // For now, return a sample count
        return 25;
    }

    // ========================================
    // IMPORT/EXPORT FUNCTIONALITY
    // ========================================

    /**
     * Initialize import/export hooks
     */
    private function init_import_export_hooks() {
        // Add import/export AJAX handlers
        add_action('wp_ajax_pbc_export_rules', array($this, 'ajax_export_rules'));
        add_action('wp_ajax_pbc_import_rules', array($this, 'ajax_import_rules'));
        add_action('wp_ajax_pbc_preview_import', array($this, 'ajax_preview_import'));
        add_action('wp_ajax_pbc_schedule_export', array($this, 'ajax_schedule_export'));
        add_action('wp_ajax_pbc_get_export_files', array($this, 'ajax_get_export_files'));
        add_action('wp_ajax_pbc_rollback_import', array($this, 'ajax_rollback_import'));
        add_action('wp_ajax_pbc_get_rollback_points', array($this, 'ajax_get_rollback_points'));
        
        // Add scheduled export hook
        add_action('pbc_scheduled_export', array($this, 'execute_scheduled_export'));
    }

    /**
     * Render import/export page
     */
    public function render_import_export_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        $import_export = new PBC_Import_Export($this->database);
        $export_files = $import_export->get_export_files();
        $rollback_points = $import_export->get_rollback_points();

        ?>
        <div class="wrap">
            <h1><?php _e('Import/Export Pricing Rules', 'price-by-country'); ?></h1>
            
            <div class="pbc-import-export-container">
                
                <!-- Export Section -->
                <div class="pbc-section pbc-export-section">
                    <h2><?php _e('Export Pricing Rules', 'price-by-country'); ?></h2>
                    
                    <form id="pbc-export-form" class="pbc-export-form">
                        <div class="pbc-export-filters">
                            <h3><?php _e('Export Filters', 'price-by-country'); ?></h3>
                            
                            <div class="pbc-filter-row">
                                <div class="pbc-filter-group">
                                    <label for="export_rule_type"><?php _e('Rule Type:', 'price-by-country'); ?></label>
                                    <select name="rule_type" id="export_rule_type">
                                        <option value="all"><?php _e('All Types', 'price-by-country'); ?></option>
                                        <option value="global"><?php _e('Global Rules', 'price-by-country'); ?></option>
                                        <option value="category"><?php _e('Category Rules', 'price-by-country'); ?></option>
                                        <option value="product"><?php _e('Product Rules', 'price-by-country'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="pbc-filter-group">
                                    <label for="export_country_code"><?php _e('Country:', 'price-by-country'); ?></label>
                                    <select name="country_code" id="export_country_code">
                                        <option value="all"><?php _e('All Countries', 'price-by-country'); ?></option>
                                        <?php echo $this->get_countries_options(); ?>
                                    </select>
                                </div>
                                
                                <div class="pbc-filter-group">
                                    <label for="export_is_active"><?php _e('Status:', 'price-by-country'); ?></label>
                                    <select name="is_active" id="export_is_active">
                                        <option value="all"><?php _e('All Statuses', 'price-by-country'); ?></option>
                                        <option value="1"><?php _e('Active Only', 'price-by-country'); ?></option>
                                        <option value="0"><?php _e('Inactive Only', 'price-by-country'); ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="pbc-filter-row">
                                <div class="pbc-filter-group">
                                    <label for="export_date_from"><?php _e('Date From:', 'price-by-country'); ?></label>
                                    <input type="date" name="date_from" id="export_date_from" />
                                </div>
                                
                                <div class="pbc-filter-group">
                                    <label for="export_date_to"><?php _e('Date To:', 'price-by-country'); ?></label>
                                    <input type="date" name="date_to" id="export_date_to" />
                                </div>
                            </div>
                        </div>
                        
                        <div class="pbc-export-actions">
                            <button type="button" id="pbc-export-now" class="button-primary">
                                <?php _e('Export Now', 'price-by-country'); ?>
                            </button>
                            <button type="button" id="pbc-schedule-export" class="button">
                                <?php _e('Schedule Export', 'price-by-country'); ?>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Export Files List -->
                    <div class="pbc-export-files">
                        <h3><?php _e('Recent Export Files', 'price-by-country'); ?></h3>
                        
                        <?php if (!empty($export_files)): ?>
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th><?php _e('Filename', 'price-by-country'); ?></th>
                                        <th><?php _e('Size', 'price-by-country'); ?></th>
                                        <th><?php _e('Created', 'price-by-country'); ?></th>
                                        <th><?php _e('Actions', 'price-by-country'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($export_files as $file): ?>
                                        <tr>
                                            <td><?php echo esc_html($file['filename']); ?></td>
                                            <td><?php echo size_format($file['size']); ?></td>
                                            <td><?php echo date('Y-m-d H:i:s', $file['created']); ?></td>
                                            <td>
                                                <a href="<?php echo esc_url($file['url']); ?>" class="button button-small" download>
                                                    <?php _e('Download', 'price-by-country'); ?>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p><?php _e('No export files found.', 'price-by-country'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Import Section -->
                <div class="pbc-section pbc-import-section">
                    <h2><?php _e('Import Pricing Rules', 'price-by-country'); ?></h2>
                    
                    <div class="pbc-import-instructions">
                        <h3><?php _e('Import Instructions', 'price-by-country'); ?></h3>
                        <ul>
                            <li><?php _e('Upload a CSV file with pricing rules data', 'price-by-country'); ?></li>
                            <li><?php _e('Required columns: Rule Type, Country Code, Adjustment Type, Adjustment Value', 'price-by-country'); ?></li>
                            <li><?php _e('Optional columns: Object ID, Object Name, Is Active', 'price-by-country'); ?></li>
                            <li><?php _e('Use the preview feature to validate your data before importing', 'price-by-country'); ?></li>
                        </ul>
                        
                        <p>
                            <a href="#" id="pbc-download-template" class="button">
                                <?php _e('Download CSV Template', 'price-by-country'); ?>
                            </a>
                        </p>
                    </div>
                    
                    <form id="pbc-import-form" class="pbc-import-form" enctype="multipart/form-data">
                        <div class="pbc-import-file">
                            <label for="import_file"><?php _e('Select CSV File:', 'price-by-country'); ?></label>
                            <input type="file" name="import_file" id="import_file" accept=".csv" required />
                        </div>
                        
                        <div class="pbc-import-options">
                            <h3><?php _e('Import Options', 'price-by-country'); ?></h3>
                            
                            <div class="pbc-option-group">
                                <label for="conflict_resolution"><?php _e('Conflict Resolution:', 'price-by-country'); ?></label>
                                <select name="conflict_resolution" id="conflict_resolution">
                                    <option value="skip"><?php _e('Skip Existing Rules', 'price-by-country'); ?></option>
                                    <option value="update"><?php _e('Update Existing Rules', 'price-by-country'); ?></option>
                                    <option value="duplicate"><?php _e('Create Duplicates', 'price-by-country'); ?></option>
                                </select>
                                <p class="description">
                                    <?php _e('How to handle rules that already exist in the database.', 'price-by-country'); ?>
                                </p>
                            </div>
                            
                            <div class="pbc-option-group">
                                <label>
                                    <input type="checkbox" name="ignore_errors" id="ignore_errors" value="1" />
                                    <?php _e('Continue import even if some rows have errors', 'price-by-country'); ?>
                                </label>
                            </div>
                            
                            <div class="pbc-option-group">
                                <label>
                                    <input type="checkbox" name="create_rollback" id="create_rollback" value="1" checked />
                                    <?php _e('Create rollback point before import', 'price-by-country'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Recommended: Create a backup that allows you to undo the import.', 'price-by-country'); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="pbc-import-actions">
                            <button type="button" id="pbc-preview-import" class="button">
                                <?php _e('Preview Import', 'price-by-country'); ?>
                            </button>
                            <button type="button" id="pbc-execute-import" class="button-primary" disabled>
                                <?php _e('Execute Import', 'price-by-country'); ?>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Import Results -->
                    <div id="pbc-import-results" class="pbc-import-results" style="display: none;">
                        <h3><?php _e('Import Results', 'price-by-country'); ?></h3>
                        <div id="pbc-import-results-content"></div>
                    </div>
                </div>
                
                <!-- Rollback Section -->
                <div class="pbc-section pbc-rollback-section">
                    <h2><?php _e('Rollback Management', 'price-by-country'); ?></h2>
                    
                    <?php if (!empty($rollback_points)): ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Rollback Point', 'price-by-country'); ?></th>
                                    <th><?php _e('Created', 'price-by-country'); ?></th>
                                    <th><?php _e('Rules Count', 'price-by-country'); ?></th>
                                    <th><?php _e('Actions', 'price-by-country'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($rollback_points as $point): ?>
                                    <tr>
                                        <td><?php echo esc_html($point['id']); ?></td>
                                        <td><?php echo esc_html($point['timestamp']); ?></td>
                                        <td><?php echo intval($point['rule_count']); ?></td>
                                        <td>
                                            <button type="button" class="button pbc-rollback-btn" data-rollback-id="<?php echo esc_attr($point['id']); ?>">
                                                <?php _e('Rollback', 'price-by-country'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p><?php _e('No rollback points available.', 'price-by-country'); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Schedule Export Modal -->
        <div id="pbc-schedule-export-modal" class="pbc-modal" style="display: none;">
            <div class="pbc-modal-content">
                <div class="pbc-modal-header">
                    <h3><?php _e('Schedule Export', 'price-by-country'); ?></h3>
                    <button type="button" class="pbc-modal-close">&times;</button>
                </div>
                <div class="pbc-modal-body">
                    <form id="pbc-schedule-form">
                        <div class="pbc-form-group">
                            <label for="schedule_frequency"><?php _e('Frequency:', 'price-by-country'); ?></label>
                            <select name="frequency" id="schedule_frequency">
                                <option value="daily"><?php _e('Daily', 'price-by-country'); ?></option>
                                <option value="weekly"><?php _e('Weekly', 'price-by-country'); ?></option>
                                <option value="monthly"><?php _e('Monthly', 'price-by-country'); ?></option>
                            </select>
                        </div>
                        
                        <div class="pbc-form-group">
                            <label for="schedule_email"><?php _e('Email Recipients:', 'price-by-country'); ?></label>
                            <input type="email" name="email_recipients" id="schedule_email" multiple placeholder="<EMAIL>" />
                            <p class="description"><?php _e('Comma-separated email addresses for notifications', 'price-by-country'); ?></p>
                        </div>
                        
                        <div class="pbc-form-group">
                            <label for="schedule_retention"><?php _e('File Retention (days):', 'price-by-country'); ?></label>
                            <input type="number" name="retention_days" id="schedule_retention" value="30" min="1" max="365" />
                        </div>
                    </form>
                </div>
                <div class="pbc-modal-footer">
                    <button type="button" class="button-primary" id="pbc-save-schedule">
                        <?php _e('Schedule Export', 'price-by-country'); ?>
                    </button>
                    <button type="button" class="button pbc-modal-close">
                        <?php _e('Cancel', 'price-by-country'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    // ========================================
    // IMPORT/EXPORT AJAX HANDLERS
    // ========================================

    /**
     * AJAX handler for exporting rules
     */
    public function ajax_export_rules() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $filters = array(
            'rule_type' => sanitize_text_field($_POST['rule_type'] ?? 'all'),
            'country_code' => sanitize_text_field($_POST['country_code'] ?? 'all'),
            'is_active' => $_POST['is_active'] ?? 'all',
            'date_from' => sanitize_text_field($_POST['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_POST['date_to'] ?? '')
        );

        $import_export = new PBC_Import_Export($this->database);
        $result = $import_export->export_pricing_rules($filters);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for importing rules
     */
    public function ajax_import_rules() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('No file uploaded or upload error', 'price-by-country'));
        }

        $import_options = array(
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'skip'),
            'ignore_errors' => isset($_POST['ignore_errors']),
            'create_rollback' => isset($_POST['create_rollback'])
        );

        // Create rollback point if requested
        $rollback_id = null;
        if ($import_options['create_rollback']) {
            $import_export = new PBC_Import_Export($this->database);
            $rollback_id = $import_export->create_rollback_point();
        }

        // Process import
        $import_export = new PBC_Import_Export($this->database);
        $result = $import_export->import_pricing_rules($_FILES['import_file']['tmp_name'], $import_options);

        if ($rollback_id) {
            $result['rollback_id'] = $rollback_id;
        }

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for previewing import
     */
    public function ajax_preview_import() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('No file uploaded or upload error', 'price-by-country'));
        }

        $import_options = array(
            'preview_only' => true,
            'conflict_resolution' => sanitize_text_field($_POST['conflict_resolution'] ?? 'skip')
        );

        $import_export = new PBC_Import_Export($this->database);
        $result = $import_export->import_pricing_rules($_FILES['import_file']['tmp_name'], $import_options);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for scheduling export
     */
    public function ajax_schedule_export() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $schedule_config = array(
            'frequency' => sanitize_text_field($_POST['frequency'] ?? 'weekly'),
            'email_recipients' => array_map('sanitize_email', explode(',', $_POST['email_recipients'] ?? '')),
            'retention_days' => intval($_POST['retention_days'] ?? 30),
            'filters' => array(
                'rule_type' => sanitize_text_field($_POST['rule_type'] ?? 'all'),
                'country_code' => sanitize_text_field($_POST['country_code'] ?? 'all'),
                'is_active' => $_POST['is_active'] ?? 'all'
            )
        );

        $import_export = new PBC_Import_Export($this->database);
        $success = $import_export->schedule_export($schedule_config);

        if ($success) {
            wp_send_json_success(__('Export scheduled successfully', 'price-by-country'));
        } else {
            wp_send_json_error(__('Failed to schedule export', 'price-by-country'));
        }
    }

    /**
     * AJAX handler for getting export files
     */
    public function ajax_get_export_files() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $import_export = new PBC_Import_Export($this->database);
        $files = $import_export->get_export_files();

        wp_send_json_success($files);
    }

    /**
     * AJAX handler for rollback
     */
    public function ajax_rollback_import() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $rollback_id = sanitize_text_field($_POST['rollback_id'] ?? '');

        if (empty($rollback_id)) {
            wp_send_json_error(__('Invalid rollback ID', 'price-by-country'));
        }

        $import_export = new PBC_Import_Export($this->database);
        $result = $import_export->execute_rollback($rollback_id);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for getting rollback points
     */
    public function ajax_get_rollback_points() {
        check_ajax_referer('pbc_admin_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'price-by-country'));
        }

        $import_export = new PBC_Import_Export($this->database);
        $points = $import_export->get_rollback_points();

        wp_send_json_success($points);
    }

    /**
     * Execute scheduled export
     *
     * @param array $schedule_config Schedule configuration
     */
    public function execute_scheduled_export($schedule_config) {
        $import_export = new PBC_Import_Export($this->database);
        $import_export->execute_scheduled_export($schedule_config);
    }

    /**
     * Render logs page
     */
    public function render_logs_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Get logs from the logger
        $logger = PBC_Logger::get_instance();
        $logs = $logger->get_logs();
        
        ?>
        <div class="wrap">
            <h1><?php _e('Price by Country Logs', 'price-by-country'); ?></h1>
            
            <div class="pbc-logs-container">
                <div class="pbc-logs-header">
                    <div class="pbc-logs-actions">
                        <button type="button" id="pbc-clear-logs" class="button">
                            <?php _e('Clear All Logs', 'price-by-country'); ?>
                        </button>
                        <button type="button" id="pbc-refresh-logs" class="button">
                            <?php _e('Refresh', 'price-by-country'); ?>
                        </button>
                    </div>
                    
                    <div class="pbc-logs-filters">
                        <select id="pbc-log-level-filter">
                            <option value=""><?php _e('All Levels', 'price-by-country'); ?></option>
                            <option value="error"><?php _e('Error', 'price-by-country'); ?></option>
                            <option value="warning"><?php _e('Warning', 'price-by-country'); ?></option>
                            <option value="info"><?php _e('Info', 'price-by-country'); ?></option>
                            <option value="debug"><?php _e('Debug', 'price-by-country'); ?></option>
                        </select>
                        
                        <input type="date" id="pbc-log-date-filter" placeholder="<?php _e('Filter by date', 'price-by-country'); ?>" />
                    </div>
                </div>
                
                <div class="pbc-logs-table-container">
                    <?php if (empty($logs)): ?>
                        <div class="pbc-no-logs">
                            <p><?php _e('No logs found.', 'price-by-country'); ?></p>
                        </div>
                    <?php else: ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col" class="column-timestamp"><?php _e('Timestamp', 'price-by-country'); ?></th>
                                    <th scope="col" class="column-level"><?php _e('Level', 'price-by-country'); ?></th>
                                    <th scope="col" class="column-message"><?php _e('Message', 'price-by-country'); ?></th>
                                    <th scope="col" class="column-context"><?php _e('Context', 'price-by-country'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($logs as $log): ?>
                                    <tr class="pbc-log-row pbc-log-<?php echo esc_attr($log['level']); ?>">
                                        <td class="column-timestamp">
                                            <?php echo esc_html(date('Y-m-d H:i:s', strtotime($log['timestamp']))); ?>
                                        </td>
                                        <td class="column-level">
                                            <span class="pbc-log-level pbc-log-level-<?php echo esc_attr($log['level']); ?>">
                                                <?php echo esc_html(ucfirst($log['level'])); ?>
                                            </span>
                                        </td>
                                        <td class="column-message">
                                            <?php echo esc_html($log['message']); ?>
                                        </td>
                                        <td class="column-context">
                                            <?php if (!empty($log['context'])): ?>
                                                <details>
                                                    <summary><?php _e('View Context', 'price-by-country'); ?></summary>
                                                    <pre><?php echo esc_html(print_r($log['context'], true)); ?></pre>
                                                </details>
                                            <?php else: ?>
                                                <span class="pbc-no-context"><?php _e('No context', 'price-by-country'); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <style>
        .pbc-logs-container {
            margin-top: 20px;
        }
        
        .pbc-logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        
        .pbc-logs-actions {
            display: flex;
            gap: 10px;
        }
        
        .pbc-logs-filters {
            display: flex;
            gap: 10px;
        }
        
        .pbc-log-level {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .pbc-log-level-error {
            background: #dc3232;
            color: white;
        }
        
        .pbc-log-level-warning {
            background: #ffb900;
            color: white;
        }
        
        .pbc-log-level-info {
            background: #00a0d2;
            color: white;
        }
        
        .pbc-log-level-debug {
            background: #666;
            color: white;
        }
        
        .pbc-no-logs {
            text-align: center;
            padding: 40px;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        
        .column-timestamp {
            width: 150px;
        }
        
        .column-level {
            width: 80px;
        }
        
        .column-context {
            width: 200px;
        }
        
        .pbc-no-context {
            color: #666;
            font-style: italic;
        }
        
        details summary {
            cursor: pointer;
            color: #0073aa;
        }
        
        details pre {
            margin-top: 10px;
            padding: 10px;
            background: #f1f1f1;
            border-radius: 3px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Clear logs
            $('#pbc-clear-logs').on('click', function() {
                if (confirm('<?php _e('Are you sure you want to clear all logs?', 'price-by-country'); ?>')) {
                    $.post(ajaxurl, {
                        action: 'pbc_clear_logs',
                        nonce: '<?php echo wp_create_nonce('pbc_admin_nonce'); ?>'
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('<?php _e('Failed to clear logs', 'price-by-country'); ?>');
                        }
                    });
                }
            });
            
            // Refresh logs
            $('#pbc-refresh-logs').on('click', function() {
                location.reload();
            });
            
            // Filter logs
            $('#pbc-log-level-filter, #pbc-log-date-filter').on('change', function() {
                var levelFilter = $('#pbc-log-level-filter').val();
                var dateFilter = $('#pbc-log-date-filter').val();
                
                $('.pbc-log-row').each(function() {
                    var $row = $(this);
                    var show = true;
                    
                    // Level filter
                    if (levelFilter && !$row.hasClass('pbc-log-' + levelFilter)) {
                        show = false;
                    }
                    
                    // Date filter
                    if (dateFilter) {
                        var rowDate = $row.find('.column-timestamp').text().split(' ')[0];
                        if (rowDate !== dateFilter) {
                            show = false;
                        }
                    }
                    
                    $row.toggle(show);
                });
            });
        });
        </script>
        <?php
    }
}
