<?php
/**
 * Logging and debugging system for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Logger Class
 */
class PBC_Logger {

    /**
     * Log levels
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * Log types for audit purposes
     */
    const TYPE_PRICING_RULE_CREATED = 'pricing_rule_created';
    const TYPE_PRICING_RULE_UPDATED = 'pricing_rule_updated';
    const TYPE_PRICING_RULE_DELETED = 'pricing_rule_deleted';
    const TYPE_COUNTRY_DETECTION = 'country_detection';
    const TYPE_PRICE_CALCULATION = 'price_calculation';
    const TYPE_ADMIN_ACTION = 'admin_action';
    const TYPE_API_REQUEST = 'api_request';
    const TYPE_SYSTEM_EVENT = 'system_event';

    /**
     * Single instance of the class
     *
     * @var PBC_Logger
     */
    private static $instance = null;

    /**
     * Log entries buffer
     *
     * @var array
     */
    private $log_buffer = [];

    /**
     * Maximum log entries to keep in memory
     */
    const MAX_BUFFER_SIZE = 100;

    /**
     * Get single instance of the class
     *
     * @return PBC_Logger
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->create_log_table();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Hook into plugin events for audit logging
        add_action('pbc_pricing_rule_created', [$this, 'log_pricing_rule_created'], 10, 2);
        add_action('pbc_pricing_rule_updated', [$this, 'log_pricing_rule_updated'], 10, 3);
        add_action('pbc_pricing_rule_deleted', [$this, 'log_pricing_rule_deleted'], 10, 2);
        
        // Hook into shutdown to flush buffer
        add_action('shutdown', [$this, 'flush_log_buffer']);
    }

    /**
     * Log a message
     *
     * @param string $level Log level
     * @param string $message Log message
     * @param string $type Log type
     * @param array $context Additional context data
     * @param int $user_id User ID (optional)
     */
    public function log($level, $message, $type = self::TYPE_SYSTEM_EVENT, $context = [], $user_id = null) {
        // Check if logging is enabled
        if (!$this->is_logging_enabled()) {
            return;
        }

        // Check if this log level should be recorded
        if (!$this->should_log_level($level)) {
            return;
        }

        $log_entry = [
            'timestamp' => current_time('timestamp'),
            'level' => $level,
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'user_id' => $user_id ?: get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'session_id' => session_id() ?: ''
        ];

        // Add to buffer
        $this->add_to_buffer($log_entry);

        // Write to WordPress error log if critical
        if ($level === self::LEVEL_CRITICAL || $level === self::LEVEL_ERROR) {
            $this->write_to_wp_log($log_entry);
        }
    }

    /**
     * Log debug message
     *
     * @param string $message Debug message
     * @param string $type Log type
     * @param array $context Context data
     */
    public function debug($message, $type = self::TYPE_SYSTEM_EVENT, $context = []) {
        $this->log(self::LEVEL_DEBUG, $message, $type, $context);
    }

    /**
     * Log info message
     *
     * @param string $message Info message
     * @param string $type Log type
     * @param array $context Context data
     */
    public function info($message, $type = self::TYPE_SYSTEM_EVENT, $context = []) {
        $this->log(self::LEVEL_INFO, $message, $type, $context);
    }

    /**
     * Log warning message
     *
     * @param string $message Warning message
     * @param string $type Log type
     * @param array $context Context data
     */
    public function warning($message, $type = self::TYPE_SYSTEM_EVENT, $context = []) {
        $this->log(self::LEVEL_WARNING, $message, $type, $context);
    }

    /**
     * Log error message
     *
     * @param string $message Error message
     * @param string $type Log type
     * @param array $context Context data
     */
    public function error($message, $type = self::TYPE_SYSTEM_EVENT, $context = []) {
        $this->log(self::LEVEL_ERROR, $message, $type, $context);
    }

    /**
     * Log critical message
     *
     * @param string $message Critical message
     * @param string $type Log type
     * @param array $context Context data
     */
    public function critical($message, $type = self::TYPE_SYSTEM_EVENT, $context = []) {
        $this->log(self::LEVEL_CRITICAL, $message, $type, $context);
    }

    /**
     * Log pricing rule creation
     *
     * @param array $rule_data Rule data
     * @param int $rule_id Created rule ID
     */
    public function log_pricing_rule_created($rule_data, $rule_id) {
        $this->info(
            sprintf('Pricing rule created: ID %d for %s %s in country %s', 
                $rule_id, 
                $rule_data['rule_type'], 
                $rule_data['object_id'] ?? 'global',
                $rule_data['country_code']
            ),
            self::TYPE_PRICING_RULE_CREATED,
            [
                'rule_id' => $rule_id,
                'rule_data' => $rule_data,
                'action' => 'create'
            ]
        );
    }

    /**
     * Log pricing rule update
     *
     * @param int $rule_id Rule ID
     * @param array $old_data Old rule data
     * @param array $new_data New rule data
     */
    public function log_pricing_rule_updated($rule_id, $old_data, $new_data) {
        $changes = $this->get_data_changes($old_data, $new_data);
        
        $this->info(
            sprintf('Pricing rule updated: ID %d - %s', $rule_id, implode(', ', $changes)),
            self::TYPE_PRICING_RULE_UPDATED,
            [
                'rule_id' => $rule_id,
                'old_data' => $old_data,
                'new_data' => $new_data,
                'changes' => $changes,
                'action' => 'update'
            ]
        );
    }

    /**
     * Log pricing rule deletion
     *
     * @param int $rule_id Rule ID
     * @param array $rule_data Rule data
     */
    public function log_pricing_rule_deleted($rule_id, $rule_data) {
        $this->info(
            sprintf('Pricing rule deleted: ID %d for %s %s in country %s', 
                $rule_id, 
                $rule_data['rule_type'], 
                $rule_data['object_id'] ?? 'global',
                $rule_data['country_code']
            ),
            self::TYPE_PRICING_RULE_DELETED,
            [
                'rule_id' => $rule_id,
                'rule_data' => $rule_data,
                'action' => 'delete'
            ]
        );
    }

    /**
     * Log country detection event
     *
     * @param string $country_code Detected country
     * @param string $method Detection method
     * @param array $context Detection context
     */
    public function log_country_detection($country_code, $method, $context = []) {
        $this->debug(
            sprintf('Country detected: %s via %s method', $country_code, $method),
            self::TYPE_COUNTRY_DETECTION,
            array_merge($context, [
                'country_code' => $country_code,
                'method' => $method
            ])
        );
    }

    /**
     * Log price calculation event
     *
     * @param int $product_id Product ID
     * @param string $country_code Country code
     * @param array $calculation_result Calculation result
     */
    public function log_price_calculation($product_id, $country_code, $calculation_result) {
        $this->debug(
            sprintf('Price calculated for product %d in %s: %s -> %s (%s)', 
                $product_id, 
                $country_code,
                $calculation_result['original_price'],
                $calculation_result['adjusted_price'],
                $calculation_result['rule_source']
            ),
            self::TYPE_PRICE_CALCULATION,
            [
                'product_id' => $product_id,
                'country_code' => $country_code,
                'calculation_result' => $calculation_result
            ]
        );
    }

    /**
     * Log admin action
     *
     * @param string $action Action performed
     * @param array $context Action context
     */
    public function log_admin_action($action, $context = []) {
        $this->info(
            sprintf('Admin action: %s', $action),
            self::TYPE_ADMIN_ACTION,
            array_merge($context, ['action' => $action])
        );
    }

    /**
     * Log API request
     *
     * @param string $endpoint API endpoint
     * @param string $method HTTP method
     * @param array $params Request parameters
     * @param array $response Response data
     */
    public function log_api_request($endpoint, $method, $params = [], $response = []) {
        $this->info(
            sprintf('API request: %s %s', $method, $endpoint),
            self::TYPE_API_REQUEST,
            [
                'endpoint' => $endpoint,
                'method' => $method,
                'params' => $params,
                'response' => $response
            ]
        );
    }

    /**
     * Get log entries from database
     *
     * @param array $filters Filter criteria
     * @param int $limit Number of entries to return
     * @param int $offset Offset for pagination
     * @return array Log entries
     */
    public function get_logs($filters = [], $limit = 50, $offset = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'pbc_logs';
        
        $where_clauses = ['1=1'];
        $where_values = [];

        // Apply filters
        if (!empty($filters['level'])) {
            $where_clauses[] = 'level = %s';
            $where_values[] = $filters['level'];
        }

        if (!empty($filters['type'])) {
            $where_clauses[] = 'type = %s';
            $where_values[] = $filters['type'];
        }

        if (!empty($filters['user_id'])) {
            $where_clauses[] = 'user_id = %d';
            $where_values[] = intval($filters['user_id']);
        }

        if (!empty($filters['date_from'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['date_to'];
        }

        if (!empty($filters['search'])) {
            $where_clauses[] = 'message LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like($filters['search']) . '%';
        }

        $where_clause = implode(' AND ', $where_clauses);
        
        // Add limit and offset values
        $where_values[] = intval($limit);
        $where_values[] = intval($offset);

        $query = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY timestamp DESC LIMIT %d OFFSET %d";
        
        if (!empty($where_values)) {
            $query = $wpdb->prepare($query, $where_values);
        }

        $results = $wpdb->get_results($query, ARRAY_A);
        
        // Unserialize context data
        foreach ($results as &$result) {
            $result['context'] = maybe_unserialize($result['context']);
            $result['timestamp'] = strtotime($result['timestamp']);
        }

        return $results;
    }

    /**
     * Get log statistics
     *
     * @param array $filters Filter criteria
     * @return array Log statistics
     */
    public function get_log_stats($filters = []) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'pbc_logs';
        
        $where_clauses = ['1=1'];
        $where_values = [];

        // Apply date filters if provided
        if (!empty($filters['date_from'])) {
            $where_clauses[] = 'timestamp >= %s';
            $where_values[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where_clauses[] = 'timestamp <= %s';
            $where_values[] = $filters['date_to'];
        }

        $where_clause = implode(' AND ', $where_clauses);

        // Get counts by level
        $level_query = "SELECT level, COUNT(*) as count FROM {$table_name} WHERE {$where_clause} GROUP BY level";
        if (!empty($where_values)) {
            $level_query = $wpdb->prepare($level_query, $where_values);
        }
        $level_stats = $wpdb->get_results($level_query, ARRAY_A);

        // Get counts by type
        $type_query = "SELECT type, COUNT(*) as count FROM {$table_name} WHERE {$where_clause} GROUP BY type";
        if (!empty($where_values)) {
            $type_query = $wpdb->prepare($type_query, $where_values);
        }
        $type_stats = $wpdb->get_results($type_query, ARRAY_A);

        // Get total count
        $total_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where_clause}";
        if (!empty($where_values)) {
            $total_query = $wpdb->prepare($total_query, $where_values);
        }
        $total_count = $wpdb->get_var($total_query);

        return [
            'total_entries' => intval($total_count),
            'by_level' => $level_stats,
            'by_type' => $type_stats
        ];
    }

    /**
     * Clear logs
     *
     * @param array $filters Filter criteria for selective clearing
     * @return bool Success status
     */
    public function clear_logs($filters = []) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'pbc_logs';

        if (empty($filters)) {
            // Clear all logs
            $result = $wpdb->query("TRUNCATE TABLE {$table_name}");
        } else {
            // Clear with filters
            $where_clauses = [];
            $where_values = [];

            if (!empty($filters['level'])) {
                $where_clauses[] = 'level = %s';
                $where_values[] = $filters['level'];
            }

            if (!empty($filters['type'])) {
                $where_clauses[] = 'type = %s';
                $where_values[] = $filters['type'];
            }

            if (!empty($filters['older_than_days'])) {
                $where_clauses[] = 'timestamp < %s';
                $where_values[] = date('Y-m-d H:i:s', strtotime("-{$filters['older_than_days']} days"));
            }

            if (!empty($where_clauses)) {
                $where_clause = implode(' AND ', $where_clauses);
                $query = "DELETE FROM {$table_name} WHERE {$where_clause}";
                $result = $wpdb->query($wpdb->prepare($query, $where_values));
            } else {
                $result = false;
            }
        }

        return $result !== false;
    }

    /**
     * Export logs to CSV
     *
     * @param array $filters Filter criteria
     * @return string CSV content
     */
    public function export_logs_csv($filters = []) {
        $logs = $this->get_logs($filters, 10000); // Get up to 10k entries
        
        $csv_content = "Timestamp,Level,Type,Message,User ID,IP Address,Context\n";
        
        foreach ($logs as $log) {
            $csv_content .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s"' . "\n",
                date('Y-m-d H:i:s', $log['timestamp']),
                $log['level'],
                $log['type'],
                str_replace('"', '""', $log['message']),
                $log['user_id'],
                $log['ip_address'],
                str_replace('"', '""', json_encode($log['context']))
            );
        }

        return $csv_content;
    }

    /**
     * Add log entry to buffer
     *
     * @param array $log_entry Log entry data
     */
    private function add_to_buffer($log_entry) {
        $this->log_buffer[] = $log_entry;
        
        // Flush buffer if it gets too large
        if (count($this->log_buffer) >= self::MAX_BUFFER_SIZE) {
            $this->flush_log_buffer();
        }
    }

    /**
     * Flush log buffer to database
     */
    public function flush_log_buffer() {
        if (empty($this->log_buffer)) {
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'pbc_logs';

        foreach ($this->log_buffer as $log_entry) {
            $wpdb->insert(
                $table_name,
                [
                    'timestamp' => date('Y-m-d H:i:s', $log_entry['timestamp']),
                    'level' => $log_entry['level'],
                    'type' => $log_entry['type'],
                    'message' => $log_entry['message'],
                    'context' => maybe_serialize($log_entry['context']),
                    'user_id' => $log_entry['user_id'],
                    'ip_address' => $log_entry['ip_address'],
                    'user_agent' => $log_entry['user_agent'],
                    'request_uri' => $log_entry['request_uri'],
                    'session_id' => $log_entry['session_id']
                ],
                [
                    '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s'
                ]
            );
        }

        // Clear buffer
        $this->log_buffer = [];
    }

    /**
     * Create log table
     */
    private function create_log_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pbc_logs';
        
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            timestamp datetime NOT NULL,
            level varchar(20) NOT NULL,
            type varchar(50) NOT NULL,
            message text NOT NULL,
            context longtext,
            user_id bigint(20) DEFAULT 0,
            ip_address varchar(45) DEFAULT '',
            user_agent text DEFAULT '',
            request_uri text DEFAULT '',
            session_id varchar(255) DEFAULT '',
            PRIMARY KEY (id),
            KEY idx_timestamp (timestamp),
            KEY idx_level (level),
            KEY idx_type (type),
            KEY idx_user_id (user_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Check if logging is enabled
     *
     * @return bool True if logging is enabled
     */
    private function is_logging_enabled() {
        $core = PBC_Core::get_instance();
        return $core->get_setting('enable_logging', false);
    }

    /**
     * Check if log level should be recorded
     *
     * @param string $level Log level
     * @return bool True if should log
     */
    private function should_log_level($level) {
        $core = PBC_Core::get_instance();
        $min_level = $core->get_setting('log_level', self::LEVEL_INFO);
        
        $level_hierarchy = [
            self::LEVEL_DEBUG => 1,
            self::LEVEL_INFO => 2,
            self::LEVEL_WARNING => 3,
            self::LEVEL_ERROR => 4,
            self::LEVEL_CRITICAL => 5
        ];

        return ($level_hierarchy[$level] ?? 0) >= ($level_hierarchy[$min_level] ?? 2);
    }

    /**
     * Write to WordPress error log
     *
     * @param array $log_entry Log entry data
     */
    private function write_to_wp_log($log_entry) {
        $log_message = sprintf(
            '[PBC %s] %s - %s (User: %d, IP: %s)',
            strtoupper($log_entry['level']),
            strtoupper($log_entry['type']),
            $log_entry['message'],
            $log_entry['user_id'],
            $log_entry['ip_address']
        );

        error_log($log_message);
    }

    /**
     * Get client IP address
     *
     * @return string Client IP address
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Get data changes between old and new data
     *
     * @param array $old_data Old data
     * @param array $new_data New data
     * @return array List of changes
     */
    private function get_data_changes($old_data, $new_data) {
        $changes = [];
        
        foreach ($new_data as $key => $new_value) {
            $old_value = $old_data[$key] ?? null;
            if ($old_value != $new_value) {
                $changes[] = "{$key}: {$old_value} -> {$new_value}";
            }
        }

        return $changes;
    }

    /**
     * Get logger instance
     *
     * @return PBC_Logger
     */
    public static function instance() {
        return self::get_instance();
    }
}