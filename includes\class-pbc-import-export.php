<?php
/**
 * Import/Export functionality for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Import Export Class
 */
class PBC_Import_Export {

    /**
     * Database instance
     *
     * @var PBC_Database
     */
    private $database;

    /**
     * Error handler instance
     *
     * @var PBC_Error_Handler
     */
    private $error_handler;

    /**
     * Logger instance
     *
     * @var PBC_Logger
     */
    private $logger;

    /**
     * Constructor
     *
     * @param PBC_Database $database Database instance
     */
    public function __construct($database) {
        $this->database = $database;
        $this->error_handler = PBC_Error_Handler::get_instance();
        $this->logger = PBC_Logger::get_instance();
    }

    // ========================================
    // EXPORT FUNCTIONALITY
    // ========================================

    /**
     * Export pricing rules to CSV
     *
     * @param array $filters Export filters
     * @return array Export result with file path or error
     */
    public function export_pricing_rules($filters = array()) {
        try {
            // Get rules based on filters
            $rules = $this->get_filtered_rules($filters);
            
            if (empty($rules)) {
                return array(
                    'success' => false,
                    'message' => __('No pricing rules found to export', 'price-by-country')
                );
            }

            // Generate CSV content
            $csv_content = $this->generate_csv_content($rules);
            
            // Create export file
            $file_path = $this->create_export_file($csv_content, $filters);
            
            // Log export activity
            $this->logger->log('info', 'Pricing rules exported', array(
                'rule_count' => count($rules),
                'filters' => $filters,
                'file_path' => $file_path
            ));

            return array(
                'success' => true,
                'file_path' => $file_path,
                'file_url' => $this->get_export_file_url($file_path),
                'rule_count' => count($rules),
                'message' => sprintf(__('Successfully exported %d pricing rules', 'price-by-country'), count($rules))
            );

        } catch (Exception $e) {
            $this->error_handler->handle_admin_error($e, array(
                'function' => __FUNCTION__,
                'filters' => $filters
            ));

            return array(
                'success' => false,
                'message' => __('Export failed: ', 'price-by-country') . $e->getMessage()
            );
        }
    }

    /**
     * Get filtered pricing rules for export
     *
     * @param array $filters Export filters
     * @return array Filtered rules
     */
    private function get_filtered_rules($filters) {
        $where_conditions = array();
        $where_values = array();

        // Filter by rule type
        if (!empty($filters['rule_type']) && $filters['rule_type'] !== 'all') {
            $where_conditions[] = "rule_type = %s";
            $where_values[] = $filters['rule_type'];
        }

        // Filter by country
        if (!empty($filters['country_code']) && $filters['country_code'] !== 'all') {
            $where_conditions[] = "country_code = %s";
            $where_values[] = $filters['country_code'];
        }

        // Filter by active status
        if (isset($filters['is_active']) && $filters['is_active'] !== 'all') {
            $where_conditions[] = "is_active = %d";
            $where_values[] = intval($filters['is_active']);
        }

        // Filter by date range
        if (!empty($filters['date_from'])) {
            $where_conditions[] = "created_at >= %s";
            $where_values[] = $filters['date_from'] . ' 00:00:00';
        }

        if (!empty($filters['date_to'])) {
            $where_conditions[] = "created_at <= %s";
            $where_values[] = $filters['date_to'] . ' 23:59:59';
        }

        // Filter by specific object IDs
        if (!empty($filters['object_ids']) && is_array($filters['object_ids'])) {
            $placeholders = implode(',', array_fill(0, count($filters['object_ids']), '%d'));
            $where_conditions[] = "object_id IN ($placeholders)";
            $where_values = array_merge($where_values, $filters['object_ids']);
        }

        // Build query
        global $wpdb;
        $table_name = $wpdb->prefix . 'pbc_pricing_rules';
        
        $sql = "SELECT * FROM {$table_name}";
        
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        $sql .= " ORDER BY rule_type, object_id, country_code";

        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Generate CSV content from rules
     *
     * @param array $rules Pricing rules
     * @return string CSV content
     */
    private function generate_csv_content($rules) {
        $csv_data = array();
        
        // Add CSV headers
        $headers = array(
            'ID',
            'Rule Type',
            'Object ID',
            'Object Name',
            'Country Code',
            'Country Name',
            'Adjustment Type',
            'Adjustment Value',
            'Is Active',
            'Created At',
            'Updated At'
        );
        
        $csv_data[] = $headers;

        // Add rule data
        foreach ($rules as $rule) {
            $object_name = $this->get_object_name($rule->rule_type, $rule->object_id);
            $country_name = $this->get_country_name($rule->country_code);
            
            $csv_data[] = array(
                $rule->id,
                ucfirst($rule->rule_type),
                $rule->object_id ?: '',
                $object_name,
                $rule->country_code,
                $country_name,
                ucfirst($rule->adjustment_type),
                $rule->adjustment_value,
                $rule->is_active ? 'Yes' : 'No',
                $rule->created_at,
                $rule->updated_at
            );
        }

        // Convert to CSV string
        return $this->array_to_csv($csv_data);
    }

    /**
     * Get object name for display
     *
     * @param string $rule_type Rule type
     * @param int $object_id Object ID
     * @return string Object name
     */
    private function get_object_name($rule_type, $object_id) {
        if (!$object_id) {
            return $rule_type === 'global' ? 'Global Rule' : '';
        }

        switch ($rule_type) {
            case 'product':
                $product = wc_get_product($object_id);
                return $product ? $product->get_name() : "Product #{$object_id} (Not Found)";
                
            case 'category':
                $term = get_term($object_id, 'product_cat');
                return $term && !is_wp_error($term) ? $term->name : "Category #{$object_id} (Not Found)";
                
            default:
                return '';
        }
    }

    /**
     * Get country name from code
     *
     * @param string $country_code Country code
     * @return string Country name
     */
    private function get_country_name($country_code) {
        $countries = WC()->countries->get_countries();
        return isset($countries[$country_code]) ? $countries[$country_code] : $country_code;
    }

    /**
     * Convert array to CSV string
     *
     * @param array $data Array data
     * @return string CSV string
     */
    private function array_to_csv($data) {
        $output = fopen('php://temp', 'r+');
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv_string = stream_get_contents($output);
        fclose($output);
        
        return $csv_string;
    }

    /**
     * Create export file
     *
     * @param string $content File content
     * @param array $filters Export filters for filename
     * @return string File path
     */
    private function create_export_file($content, $filters = array()) {
        // Create exports directory if it doesn't exist
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/pbc-exports';
        
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
            
            // Add .htaccess to protect directory
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents($export_dir . '/.htaccess', $htaccess_content);
        }

        // Generate filename
        $filename = $this->generate_export_filename($filters);
        $file_path = $export_dir . '/' . $filename;

        // Write file
        if (file_put_contents($file_path, $content) === false) {
            throw new Exception('Failed to create export file');
        }

        return $file_path;
    }

    /**
     * Generate export filename
     *
     * @param array $filters Export filters
     * @return string Filename
     */
    private function generate_export_filename($filters = array()) {
        $filename_parts = array('pbc-pricing-rules');
        
        // Add filter info to filename
        if (!empty($filters['rule_type']) && $filters['rule_type'] !== 'all') {
            $filename_parts[] = $filters['rule_type'];
        }
        
        if (!empty($filters['country_code']) && $filters['country_code'] !== 'all') {
            $filename_parts[] = $filters['country_code'];
        }
        
        // Add timestamp
        $filename_parts[] = date('Y-m-d-H-i-s');
        
        return implode('-', $filename_parts) . '.csv';
    }

    /**
     * Get export file URL
     *
     * @param string $file_path File path
     * @return string File URL
     */
    private function get_export_file_url($file_path) {
        $upload_dir = wp_upload_dir();
        return str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $file_path);
    }

    /**
     * Schedule automated export
     *
     * @param array $schedule_config Schedule configuration
     * @return bool Success status
     */
    public function schedule_export($schedule_config) {
        try {
            $hook_name = 'pbc_scheduled_export';
            
            // Clear existing schedule
            wp_clear_scheduled_hook($hook_name, array($schedule_config));
            
            // Schedule new export
            $next_run = wp_next_scheduled($hook_name, array($schedule_config));
            
            if (!$next_run) {
                $frequency = $schedule_config['frequency'] ?? 'weekly';
                $next_run_time = $this->calculate_next_run_time($frequency);
                
                wp_schedule_event($next_run_time, $frequency, $hook_name, array($schedule_config));
                
                // Log scheduling
                $this->logger->log('info', 'Export scheduled', array(
                    'frequency' => $frequency,
                    'next_run' => date('Y-m-d H:i:s', $next_run_time),
                    'config' => $schedule_config
                ));
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            $this->error_handler->handle_admin_error($e, array(
                'function' => __FUNCTION__,
                'schedule_config' => $schedule_config
            ));
            
            return false;
        }
    }

    /**
     * Calculate next run time for scheduled export
     *
     * @param string $frequency Frequency (daily, weekly, monthly)
     * @return int Timestamp
     */
    private function calculate_next_run_time($frequency) {
        $now = current_time('timestamp');
        
        switch ($frequency) {
            case 'daily':
                return strtotime('tomorrow 2:00 AM', $now);
                
            case 'weekly':
                return strtotime('next monday 2:00 AM', $now);
                
            case 'monthly':
                return strtotime('first day of next month 2:00 AM', $now);
                
            default:
                return strtotime('+1 week', $now);
        }
    }

    /**
     * Execute scheduled export
     *
     * @param array $schedule_config Schedule configuration
     */
    public function execute_scheduled_export($schedule_config) {
        try {
            $filters = $schedule_config['filters'] ?? array();
            $result = $this->export_pricing_rules($filters);
            
            if ($result['success']) {
                // Send email notification if configured
                if (!empty($schedule_config['email_recipients'])) {
                    $this->send_export_notification($result, $schedule_config);
                }
                
                // Clean up old export files
                $this->cleanup_old_exports($schedule_config['retention_days'] ?? 30);
            }
            
        } catch (Exception $e) {
            $this->error_handler->handle_admin_error($e, array(
                'function' => __FUNCTION__,
                'schedule_config' => $schedule_config
            ));
        }
    }

    /**
     * Send export notification email
     *
     * @param array $export_result Export result
     * @param array $schedule_config Schedule configuration
     */
    private function send_export_notification($export_result, $schedule_config) {
        $recipients = $schedule_config['email_recipients'];
        $subject = sprintf(__('Price by Country Export - %s', 'price-by-country'), date('Y-m-d H:i'));
        
        $message = sprintf(
            __('Your scheduled pricing rules export has been completed successfully.

Export Details:
- Rules exported: %d
- File: %s
- Generated: %s

You can download the file from your WordPress admin area.', 'price-by-country'),
            $export_result['rule_count'],
            basename($export_result['file_path']),
            date('Y-m-d H:i:s')
        );
        
        wp_mail($recipients, $subject, $message);
    }

    /**
     * Clean up old export files
     *
     * @param int $retention_days Number of days to keep files
     */
    private function cleanup_old_exports($retention_days = 30) {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/pbc-exports';
        
        if (!is_dir($export_dir)) {
            return;
        }
        
        $cutoff_time = time() - ($retention_days * 24 * 60 * 60);
        $files = glob($export_dir . '/pbc-pricing-rules-*.csv');
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }
    }

    /**
     * Get available export files
     *
     * @return array List of export files
     */
    public function get_export_files() {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/pbc-exports';
        
        if (!is_dir($export_dir)) {
            return array();
        }
        
        $files = glob($export_dir . '/pbc-pricing-rules-*.csv');
        $export_files = array();
        
        foreach ($files as $file) {
            $export_files[] = array(
                'filename' => basename($file),
                'path' => $file,
                'url' => $this->get_export_file_url($file),
                'size' => filesize($file),
                'created' => filemtime($file)
            );
        }
        
        // Sort by creation time (newest first)
        usort($export_files, function($a, $b) {
            return $b['created'] - $a['created'];
        });
        
        return $export_files;
    }

    // ========================================
    // IMPORT FUNCTIONALITY
    // ========================================

    /**
     * Import pricing rules from CSV
     *
     * @param string $file_path Path to CSV file
     * @param array $import_options Import options
     * @return array Import result
     */
    public function import_pricing_rules($file_path, $import_options = array()) {
        try {
            // Validate file
            if (!file_exists($file_path)) {
                throw new Exception(__('Import file not found', 'price-by-country'));
            }

            // Parse CSV file
            $csv_data = $this->parse_csv_file($file_path);
            
            if (empty($csv_data)) {
                throw new Exception(__('No data found in import file', 'price-by-country'));
            }

            // Validate CSV structure
            $validation_result = $this->validate_csv_structure($csv_data);
            if (!$validation_result['valid']) {
                throw new Exception($validation_result['message']);
            }

            // Process import with preview if requested
            if (!empty($import_options['preview_only'])) {
                return $this->preview_import($csv_data, $import_options);
            }

            // Execute actual import
            return $this->execute_import($csv_data, $import_options);

        } catch (Exception $e) {
            $this->error_handler->handle_admin_error($e, array(
                'function' => __FUNCTION__,
                'file_path' => $file_path,
                'import_options' => $import_options
            ));

            return array(
                'success' => false,
                'message' => __('Import failed: ', 'price-by-country') . $e->getMessage()
            );
        }
    }

    /**
     * Parse CSV file
     *
     * @param string $file_path File path
     * @return array Parsed CSV data
     */
    private function parse_csv_file($file_path) {
        $csv_data = array();
        
        if (($handle = fopen($file_path, 'r')) !== false) {
            $header = null;
            
            while (($row = fgetcsv($handle)) !== false) {
                if ($header === null) {
                    $header = $row;
                } else {
                    $csv_data[] = array_combine($header, $row);
                }
            }
            
            fclose($handle);
        }
        
        return $csv_data;
    }

    /**
     * Validate CSV structure
     *
     * @param array $csv_data CSV data
     * @return array Validation result
     */
    private function validate_csv_structure($csv_data) {
        $required_columns = array(
            'Rule Type',
            'Country Code',
            'Adjustment Type',
            'Adjustment Value'
        );

        if (empty($csv_data)) {
            return array(
                'valid' => false,
                'message' => __('CSV file is empty', 'price-by-country')
            );
        }

        $first_row = reset($csv_data);
        $available_columns = array_keys($first_row);

        foreach ($required_columns as $required_column) {
            if (!in_array($required_column, $available_columns)) {
                return array(
                    'valid' => false,
                    'message' => sprintf(__('Missing required column: %s', 'price-by-country'), $required_column)
                );
            }
        }

        return array('valid' => true);
    }

    /**
     * Preview import data
     *
     * @param array $csv_data CSV data
     * @param array $import_options Import options
     * @return array Preview result
     */
    private function preview_import($csv_data, $import_options) {
        $preview_data = array();
        $validation_errors = array();
        $conflicts = array();
        
        $valid_count = 0;
        $error_count = 0;
        
        foreach ($csv_data as $index => $row) {
            $row_number = $index + 2; // +2 because index starts at 0 and we skip header
            
            // Validate row data
            $validation = $this->validate_import_row($row, $row_number);
            
            if ($validation['valid']) {
                $valid_count++;
                
                // Check for conflicts with existing rules
                $conflict = $this->check_import_conflicts($validation['data']);
                if ($conflict) {
                    $conflicts[] = array(
                        'row' => $row_number,
                        'data' => $validation['data'],
                        'conflict' => $conflict
                    );
                }
                
                $preview_data[] = array(
                    'row' => $row_number,
                    'data' => $validation['data'],
                    'status' => 'valid',
                    'action' => $conflict ? 'update' : 'create'
                );
            } else {
                $error_count++;
                $validation_errors[] = array(
                    'row' => $row_number,
                    'errors' => $validation['errors']
                );
                
                $preview_data[] = array(
                    'row' => $row_number,
                    'data' => $row,
                    'status' => 'error',
                    'errors' => $validation['errors']
                );
            }
        }

        return array(
            'success' => true,
            'preview' => true,
            'total_rows' => count($csv_data),
            'valid_rows' => $valid_count,
            'error_rows' => $error_count,
            'conflicts' => count($conflicts),
            'preview_data' => $preview_data,
            'validation_errors' => $validation_errors,
            'conflict_data' => $conflicts,
            'message' => sprintf(
                __('Preview: %d valid rows, %d errors, %d conflicts found', 'price-by-country'),
                $valid_count,
                $error_count,
                count($conflicts)
            )
        );
    }

    /**
     * Execute import
     *
     * @param array $csv_data CSV data
     * @param array $import_options Import options
     * @return array Import result
     */
    private function execute_import($csv_data, $import_options) {
        global $wpdb;
        
        $created_count = 0;
        $updated_count = 0;
        $skipped_count = 0;
        $error_count = 0;
        $import_errors = array();
        
        // Start transaction for data integrity
        $wpdb->query('START TRANSACTION');
        
        try {
            foreach ($csv_data as $index => $row) {
                $row_number = $index + 2;
                
                // Validate row
                $validation = $this->validate_import_row($row, $row_number);
                
                if (!$validation['valid']) {
                    $error_count++;
                    $import_errors[] = array(
                        'row' => $row_number,
                        'errors' => $validation['errors']
                    );
                    continue;
                }
                
                $rule_data = $validation['data'];
                
                // Check for existing rule
                $existing_rule = $this->find_existing_rule($rule_data);
                
                if ($existing_rule) {
                    // Handle conflict based on import options
                    $conflict_action = $import_options['conflict_resolution'] ?? 'skip';
                    
                    switch ($conflict_action) {
                        case 'update':
                            if ($this->database->update_pricing_rule($existing_rule->id, $rule_data)) {
                                $updated_count++;
                            } else {
                                $error_count++;
                                $import_errors[] = array(
                                    'row' => $row_number,
                                    'errors' => array(__('Failed to update existing rule', 'price-by-country'))
                                );
                            }
                            break;
                            
                        case 'skip':
                            $skipped_count++;
                            break;
                            
                        case 'duplicate':
                            // Create new rule even if similar exists
                            $new_rule_id = $this->database->create_pricing_rule($rule_data);
                            if ($new_rule_id) {
                                $created_count++;
                            } else {
                                $error_count++;
                                $import_errors[] = array(
                                    'row' => $row_number,
                                    'errors' => array(__('Failed to create rule', 'price-by-country'))
                                );
                            }
                            break;
                    }
                } else {
                    // Create new rule
                    $new_rule_id = $this->database->create_pricing_rule($rule_data);
                    if ($new_rule_id) {
                        $created_count++;
                    } else {
                        $error_count++;
                        $import_errors[] = array(
                            'row' => $row_number,
                            'errors' => array(__('Failed to create rule', 'price-by-country'))
                        );
                    }
                }
            }
            
            // Commit transaction if no critical errors
            if ($error_count === 0 || !empty($import_options['ignore_errors'])) {
                $wpdb->query('COMMIT');
                
                // Log successful import
                $this->logger->log('info', 'Pricing rules imported', array(
                    'total_rows' => count($csv_data),
                    'created' => $created_count,
                    'updated' => $updated_count,
                    'skipped' => $skipped_count,
                    'errors' => $error_count
                ));
                
                return array(
                    'success' => true,
                    'total_rows' => count($csv_data),
                    'created' => $created_count,
                    'updated' => $updated_count,
                    'skipped' => $skipped_count,
                    'errors' => $error_count,
                    'import_errors' => $import_errors,
                    'message' => sprintf(
                        __('Import completed: %d created, %d updated, %d skipped, %d errors', 'price-by-country'),
                        $created_count,
                        $updated_count,
                        $skipped_count,
                        $error_count
                    )
                );
            } else {
                throw new Exception(__('Import failed due to validation errors', 'price-by-country'));
            }
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            throw $e;
        }
    }

    /**
     * Validate import row data
     *
     * @param array $row Row data
     * @param int $row_number Row number for error reporting
     * @return array Validation result
     */
    private function validate_import_row($row, $row_number) {
        $errors = array();
        $rule_data = array();

        // Validate rule type
        $rule_type = strtolower(trim($row['Rule Type'] ?? ''));
        if (!in_array($rule_type, array('global', 'category', 'product'))) {
            $errors[] = __('Invalid rule type. Must be: global, category, or product', 'price-by-country');
        } else {
            $rule_data['rule_type'] = $rule_type;
        }

        // Validate object ID for non-global rules
        $object_id = trim($row['Object ID'] ?? '');
        if ($rule_type !== 'global') {
            if (empty($object_id) || !is_numeric($object_id)) {
                $errors[] = __('Object ID is required for category and product rules', 'price-by-country');
            } else {
                $object_id = intval($object_id);
                
                // Validate object exists
                if ($rule_type === 'product') {
                    $product = wc_get_product($object_id);
                    if (!$product) {
                        $errors[] = sprintf(__('Product with ID %d not found', 'price-by-country'), $object_id);
                    }
                } elseif ($rule_type === 'category') {
                    $term = get_term($object_id, 'product_cat');
                    if (!$term || is_wp_error($term)) {
                        $errors[] = sprintf(__('Category with ID %d not found', 'price-by-country'), $object_id);
                    }
                }
                
                $rule_data['object_id'] = $object_id;
            }
        } else {
            $rule_data['object_id'] = null;
        }

        // Validate country code
        $country_code = strtoupper(trim($row['Country Code'] ?? ''));
        if (empty($country_code) || strlen($country_code) !== 2) {
            $errors[] = __('Invalid country code. Must be 2-letter ISO code', 'price-by-country');
        } else {
            $countries = WC()->countries->get_countries();
            if (!isset($countries[$country_code])) {
                $errors[] = sprintf(__('Unknown country code: %s', 'price-by-country'), $country_code);
            } else {
                $rule_data['country_code'] = $country_code;
            }
        }

        // Validate adjustment type
        $adjustment_type = strtolower(trim($row['Adjustment Type'] ?? ''));
        if (!in_array($adjustment_type, array('fixed', 'percentage'))) {
            $errors[] = __('Invalid adjustment type. Must be: fixed or percentage', 'price-by-country');
        } else {
            $rule_data['adjustment_type'] = $adjustment_type;
        }

        // Validate adjustment value
        $adjustment_value = trim($row['Adjustment Value'] ?? '');
        if (!is_numeric($adjustment_value)) {
            $errors[] = __('Adjustment value must be numeric', 'price-by-country');
        } else {
            $adjustment_value = floatval($adjustment_value);
            
            // Validate percentage range
            if ($adjustment_type === 'percentage' && ($adjustment_value < -100 || $adjustment_value > 1000)) {
                $errors[] = __('Percentage adjustment must be between -100 and 1000', 'price-by-country');
            }
            
            $rule_data['adjustment_value'] = $adjustment_value;
        }

        // Validate active status
        $is_active = trim($row['Is Active'] ?? 'Yes');
        $rule_data['is_active'] = in_array(strtolower($is_active), array('yes', '1', 'true', 'active')) ? 1 : 0;

        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'data' => $rule_data
        );
    }

    /**
     * Check for import conflicts with existing rules
     *
     * @param array $rule_data Rule data
     * @return object|null Existing rule if conflict found
     */
    private function check_import_conflicts($rule_data) {
        return $this->find_existing_rule($rule_data);
    }

    /**
     * Find existing rule that matches import data
     *
     * @param array $rule_data Rule data
     * @return object|null Existing rule
     */
    private function find_existing_rule($rule_data) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'pbc_pricing_rules';
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$table_name} 
             WHERE rule_type = %s 
             AND object_id = %s 
             AND country_code = %s 
             LIMIT 1",
            $rule_data['rule_type'],
            $rule_data['object_id'],
            $rule_data['country_code']
        );
        
        return $wpdb->get_row($sql);
    }

    /**
     * Create import rollback point
     *
     * @return string Rollback ID
     */
    public function create_rollback_point() {
        $rollback_id = 'pbc_rollback_' . time() . '_' . wp_generate_password(8, false);
        
        // Export current state for rollback
        $current_rules = $this->database->get_all_pricing_rules();
        $rollback_data = array(
            'timestamp' => current_time('mysql'),
            'rules' => $current_rules,
            'user_id' => get_current_user_id()
        );
        
        // Store rollback data
        update_option($rollback_id, $rollback_data);
        
        // Clean up old rollback points (keep only last 5)
        $this->cleanup_rollback_points();
        
        return $rollback_id;
    }

    /**
     * Execute rollback to previous state
     *
     * @param string $rollback_id Rollback ID
     * @return array Rollback result
     */
    public function execute_rollback($rollback_id) {
        try {
            $rollback_data = get_option($rollback_id);
            
            if (!$rollback_data) {
                throw new Exception(__('Rollback point not found', 'price-by-country'));
            }
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'pbc_pricing_rules';
            
            // Start transaction
            $wpdb->query('START TRANSACTION');
            
            // Clear current rules
            $wpdb->query("DELETE FROM {$table_name}");
            
            // Restore rules from rollback point
            $restored_count = 0;
            foreach ($rollback_data['rules'] as $rule) {
                $rule_data = array(
                    'rule_type' => $rule->rule_type,
                    'object_id' => $rule->object_id,
                    'country_code' => $rule->country_code,
                    'adjustment_type' => $rule->adjustment_type,
                    'adjustment_value' => $rule->adjustment_value,
                    'is_active' => $rule->is_active
                );
                
                if ($this->database->create_pricing_rule($rule_data)) {
                    $restored_count++;
                }
            }
            
            $wpdb->query('COMMIT');
            
            // Log rollback
            $this->logger->log('info', 'Import rollback executed', array(
                'rollback_id' => $rollback_id,
                'restored_rules' => $restored_count,
                'rollback_timestamp' => $rollback_data['timestamp']
            ));
            
            return array(
                'success' => true,
                'restored_rules' => $restored_count,
                'message' => sprintf(__('Successfully rolled back to %s. %d rules restored.', 'price-by-country'), $rollback_data['timestamp'], $restored_count)
            );
            
        } catch (Exception $e) {
            global $wpdb;
            $wpdb->query('ROLLBACK');
            
            $this->error_handler->handle_admin_error($e, array(
                'function' => __FUNCTION__,
                'rollback_id' => $rollback_id
            ));
            
            return array(
                'success' => false,
                'message' => __('Rollback failed: ', 'price-by-country') . $e->getMessage()
            );
        }
    }

    /**
     * Get available rollback points
     *
     * @return array Rollback points
     */
    public function get_rollback_points() {
        global $wpdb;
        
        $rollback_options = $wpdb->get_results(
            "SELECT option_name, option_value 
             FROM {$wpdb->options} 
             WHERE option_name LIKE 'pbc_rollback_%' 
             ORDER BY option_name DESC 
             LIMIT 10"
        );
        
        $rollback_points = array();
        
        foreach ($rollback_options as $option) {
            $data = maybe_unserialize($option->option_value);
            if (is_array($data) && isset($data['timestamp'])) {
                $rollback_points[] = array(
                    'id' => $option->option_name,
                    'timestamp' => $data['timestamp'],
                    'rule_count' => count($data['rules']),
                    'user_id' => $data['user_id'] ?? 0
                );
            }
        }
        
        return $rollback_points;
    }

    /**
     * Clean up old rollback points
     */
    private function cleanup_rollback_points() {
        global $wpdb;
        
        $old_rollbacks = $wpdb->get_results(
            "SELECT option_name 
             FROM {$wpdb->options} 
             WHERE option_name LIKE 'pbc_rollback_%' 
             ORDER BY option_name DESC 
             LIMIT 10, 999999"
        );
        
        foreach ($old_rollbacks as $rollback) {
            delete_option($rollback->option_name);
        }
    }
}